# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
dist
dist-ssr
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# 环境变量文件（如果包含敏感信息）
.env.local
.env.*.local

# 构建输出
dist/
build/

# 缓存文件
.cache/
.temp/
.tmp/

# 操作系统文件
Thumbs.db
ehthumbs.db
Desktop.ini

# 编辑器备份文件
*~
*.swp
*.swo

# 自动生成的类型文件
auto-imports.d.ts
components.d.ts