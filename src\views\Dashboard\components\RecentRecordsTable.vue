<template>
  <el-card class="recent-records" shadow="hover">
    <template #header>
      <div class="card-header">
        <div class="header-left">
          <el-icon class="header-icon"><Document /></el-icon>
          <span class="header-title">最近记录</span>
          <el-tag size="small" type="info">{{ records.length }}</el-tag>
        </div>
        <div class="header-right">
          <el-button size="small" @click="refreshRecords" :loading="loading">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
          <el-button size="small" type="primary" @click="viewAllRecords">
            <el-icon><More /></el-icon>
            查看全部
          </el-button>
        </div>
      </div>
    </template>

    <div class="table-container">
      <el-table 
        :data="records" 
        v-loading="loading"
        size="small"
        style="width: 100%"
        max-height="250"
      >
        <el-table-column prop="plateNumber" label="车牌号" width="100" align="center">
          <template #default="{ row }">
            <el-tag size="small" type="primary" effect="dark">
              {{ row.plateNumber }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="weight" label="重量" width="80" align="center">
          <template #default="{ row }">
            <span :class="getWeightClass(row.weight)">
              {{ row.weight }}吨
            </span>
          </template>
        </el-table-column>
        
        <el-table-column prop="status" label="状态" width="70" align="center">
          <template #default="{ row }">
            <el-tag 
              :type="row.status === 'IN' ? 'success' : 'warning'" 
              size="small"
              effect="dark"
            >
              {{ row.status === 'IN' ? '入场' : '出场' }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="timestamp" label="时间" align="center" min-width="130">
          <template #default="{ row }">
            <div class="time-display">
              <div class="time-main">{{ formatTime(row.timestamp) }}</div>
              <div class="time-relative">{{ getRelativeTime(row.timestamp) }}</div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="80" align="center" fixed="right">
          <template #default="{ row }">
            <el-dropdown @command="handleCommand">
              <el-button size="small" type="primary" link>
                <el-icon><MoreFilled /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item :command="`view:${row.id}`">
                    <el-icon><View /></el-icon>详情
                  </el-dropdown-item>
                  <el-dropdown-item :command="`images:${row.id}`">
                    <el-icon><Picture /></el-icon>照片
                  </el-dropdown-item>
                  <el-dropdown-item :command="`copy:${row.id}`" divided>
                    <el-icon><CopyDocument /></el-icon>复制
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 空状态 -->
      <div v-if="!loading && records.length === 0" class="empty-state">
        <el-empty description="暂无记录" :image-size="80">
          <el-button type="primary" @click="refreshRecords">刷新数据</el-button>
        </el-empty>
      </div>
    </div>

    <!-- 快速统计 -->
    <div class="quick-stats">
      <div class="stat-item">
        <span class="stat-label">今日入场:</span>
        <span class="stat-value stat-in">{{ todayInCount }}辆</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">今日出场:</span>
        <span class="stat-value stat-out">{{ todayOutCount }}辆</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">总重量:</span>
        <span class="stat-value">{{ totalWeight }}吨</span>
      </div>
    </div>
  </el-card>
</template>

<script setup lang="ts">
// 最近记录表格组件
// 作者：仕伟

import { vehicleApi, type VehicleRecord } from '@/services/api'
import { useWebSocket } from '@/services/websocket'
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'

// 启用相对时间插件
dayjs.extend(relativeTime)

const router = useRouter()

// 组件状态
const records = ref<VehicleRecord[]>([])
const loading = ref(false)

// WebSocket连接
const { subscribe } = useWebSocket()

// 计算统计数据
const todayInCount = computed(() => {
  const today = dayjs().startOf('day')
  return records.value.filter(r => 
    r.status === 'IN' && dayjs(r.timestamp).isAfter(today)
  ).length
})

const todayOutCount = computed(() => {
  const today = dayjs().startOf('day')
  return records.value.filter(r => 
    r.status === 'OUT' && dayjs(r.timestamp).isAfter(today)
  ).length
})

const totalWeight = computed(() => {
  const total = records.value.reduce((sum, record) => sum + record.weight, 0)
  return total.toFixed(1)
})

// 获取重量样式类
const getWeightClass = (weight: number) => {
  if (weight > 40) return 'weight-overload'
  if (weight < 5) return 'weight-light'
  return 'weight-normal'
}

// 格式化时间
const formatTime = (timestamp: number) => {
  return dayjs(timestamp).format('MM-DD HH:mm:ss')
}

// 获取相对时间
const getRelativeTime = (timestamp: number) => {
  return dayjs(timestamp).fromNow()
}

// 刷新记录
const refreshRecords = async () => {
  loading.value = true
  
  try {
    // 获取最近20条记录
    const result = await vehicleApi.queryVehicleHistory({
      page: 1,
      limit: 20
    })
    
    records.value = result.sort((a, b) => b.timestamp - a.timestamp)
  } catch (error) {
    ElMessage.error('获取记录失败')
    console.error('获取记录失败:', error)
  } finally {
    loading.value = false
  }
}

// 查看全部记录
const viewAllRecords = () => {
  router.push('/history')
}

// 处理下拉菜单命令
const handleCommand = (command: string) => {
  const [action, id] = command.split(':')
  const record = records.value.find(r => r.id === id)
  
  if (!record) return
  
  switch (action) {
    case 'view':
      ElMessage.info(`查看详情: ${record.plateNumber}`)
      // TODO: 打开详情对话框
      break
      
    case 'images':
      ElMessage.info(`查看照片: ${record.plateNumber}`)
      // TODO: 打开图片预览
      break
      
    case 'copy':
      // 复制车牌号到剪贴板
      if (navigator.clipboard) {
        navigator.clipboard.writeText(record.plateNumber).then(() => {
          ElMessage.success(`已复制车牌号: ${record.plateNumber}`)
        })
      } else {
        ElMessage.warning('浏览器不支持复制功能')
      }
      break
  }
}

// 添加新记录到列表顶部
const addNewRecord = (record: VehicleRecord) => {
  records.value.unshift(record)
  
  // 保持最多20条记录
  if (records.value.length > 20) {
    records.value = records.value.slice(0, 20)
  }
  
  // 显示通知
  ElMessage.success(`新增${record.status === 'IN' ? '入场' : '出场'}记录: ${record.plateNumber}`)
}

// 页面挂载时获取数据
onMounted(() => {
  refreshRecords()
  
  // 订阅实时数据更新
  subscribe('VEHICLE_RECORD', (data) => {
    // 当有新的车辆记录时，添加到列表
    addNewRecord(data)
  })
  
  // 定期刷新数据
  const timer = setInterval(() => {
    refreshRecords()
  }, 60000) // 每分钟刷新一次
  
  onUnmounted(() => {
    clearInterval(timer)
  })
})
</script>

<style scoped lang="scss">
.recent-records {
  height: 100%;
  
  :deep(.el-card__body) {
    height: calc(100% - 60px);
    padding: 0;
    display: flex;
    flex-direction: column;
  }

  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 0 var(--spacing-md);

    .header-left {
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);

      .header-icon {
        color: var(--el-color-primary);
        font-size: 18px;
      }

      .header-title {
        font-size: 16px;
        font-weight: 600;
        color: var(--text-primary);
      }
    }

    .header-right {
      .el-button {
        margin-left: var(--spacing-xs);

        .el-icon {
          margin-right: var(--spacing-xs);
        }
      }
    }
  }

  .table-container {
    flex: 1;
    padding: 0 var(--spacing-md);
    overflow: hidden;

    :deep(.el-table) {
      .time-display {
        .time-main {
          font-size: 12px;
          color: var(--text-primary);
          font-family: 'Courier New', monospace;
        }

        .time-relative {
          font-size: 10px;
          color: var(--text-placeholder);
          margin-top: 1px;
        }
      }

      .weight-normal {
        color: var(--text-primary);
      }

      .weight-overload {
        color: var(--color-weight-overload);
        font-weight: 600;
      }

      .weight-light {
        color: var(--text-secondary);
      }
    }

    .empty-state {
      padding: var(--spacing-xl) 0;
    }
  }

  .quick-stats {
    border-top: 1px solid var(--border-base);
    padding: var(--spacing-md);
    display: flex;
    justify-content: space-between;
    background: var(--bg-secondary);

    .stat-item {
      text-align: center;
      flex: 1;

      .stat-label {
        display: block;
        font-size: 12px;
        color: var(--text-secondary);
        margin-bottom: var(--spacing-xs);
      }

      .stat-value {
        display: block;
        font-size: 16px;
        font-weight: 600;
        color: var(--text-primary);

        &.stat-in {
          color: var(--el-color-success);
        }

        &.stat-out {
          color: var(--el-color-warning);
        }
      }
    }
  }
}
</style>