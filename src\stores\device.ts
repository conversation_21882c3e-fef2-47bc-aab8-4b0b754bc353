import { defineStore } from 'pinia'

// 设备状态管理 Store
// 作者：仕伟

export type DeviceStatus = 'online' | 'offline' | 'warning' | 'error'

interface DeviceInfo {
  status: DeviceStatus
  lastUpdate: number
  message?: string
}

interface DeviceState {
  status: {
    camera: DeviceStatus
    scale: DeviceStatus
    gate: DeviceStatus
  }
  info: {
    camera: DeviceInfo
    scale: DeviceInfo  
    gate: DeviceInfo
  }
}

export const useDeviceStore = defineStore('device', {
  state: (): DeviceState => ({
    status: {
      camera: 'online',
      scale: 'online', 
      gate: 'online'
    },
    info: {
      camera: {
        status: 'online',
        lastUpdate: Date.now(),
        message: '摄像头工作正常'
      },
      scale: {
        status: 'online',
        lastUpdate: Date.now(),
        message: '地磅连接正常'
      },
      gate: {
        status: 'online',
        lastUpdate: Date.now(),
        message: '道闸控制正常'
      }
    }
  }),

  getters: {
    // 获取所有设备的整体状态
    overallStatus(): DeviceStatus {
      const statuses = Object.values(this.status)
      
      if (statuses.includes('error')) return 'error'
      if (statuses.includes('offline')) return 'offline'  
      if (statuses.includes('warning')) return 'warning'
      return 'online'
    },

    // 获取在线设备数量
    onlineCount(): number {
      return Object.values(this.status).filter(status => status === 'online').length
    },

    // 获取离线设备数量  
    offlineCount(): number {
      return Object.values(this.status).filter(status => status === 'offline').length
    },

    // 设备总数
    totalCount(): number {
      return Object.keys(this.status).length
    }
  },

  actions: {
    // 更新单个设备状态
    updateDeviceStatus(device: keyof DeviceState['status'], status: DeviceStatus, message?: string) {
      this.status[device] = status
      this.info[device] = {
        status,
        lastUpdate: Date.now(),
        message: message || this.info[device].message
      }
    },

    // 更新摄像头状态
    updateCameraStatus(status: DeviceStatus, message?: string) {
      this.updateDeviceStatus('camera', status, message)
    },

    // 更新地磅状态
    updateScaleStatus(status: DeviceStatus, message?: string) {
      this.updateDeviceStatus('scale', status, message)
    },

    // 更新道闸状态
    updateGateStatus(status: DeviceStatus, message?: string) {
      this.updateDeviceStatus('gate', status, message)
    },

    // 批量更新设备状态
    updateAllDeviceStatus(statusMap: Partial<DeviceState['status']>) {
      Object.entries(statusMap).forEach(([device, status]) => {
        if (status) {
          this.updateDeviceStatus(device as keyof DeviceState['status'], status)
        }
      })
    },

    // 重置所有设备状态为在线
    resetAllDeviceStatus() {
      this.updateAllDeviceStatus({
        camera: 'online',
        scale: 'online',
        gate: 'online'
      })
    },

    // 模拟设备状态变化（用于测试）
    simulateDeviceStatus() {
      const devices: Array<keyof DeviceState['status']> = ['camera', 'scale', 'gate']
      const statuses: DeviceStatus[] = ['online', 'offline', 'warning']
      
      devices.forEach(device => {
        const randomStatus = statuses[Math.floor(Math.random() * statuses.length)]
        this.updateDeviceStatus(device, randomStatus)
      })
    },

    // 检查设备连接超时
    checkDeviceTimeout() {
      const timeout = 30000 // 30秒超时
      const now = Date.now()
      
      Object.entries(this.info).forEach(([device, info]) => {
        if (now - info.lastUpdate > timeout && info.status === 'online') {
          this.updateDeviceStatus(
            device as keyof DeviceState['status'], 
            'offline', 
            '设备连接超时'
          )
        }
      })
    },

    // 初始化设备状态监控
    initDeviceMonitoring() {
      // 定期检查设备超时
      setInterval(() => {
        this.checkDeviceTimeout()
      }, 10000) // 每10秒检查一次

      // TODO: 这里可以添加真实的设备状态监听逻辑
      // 比如 WebSocket 连接、定期轮询等
    }
  }
})