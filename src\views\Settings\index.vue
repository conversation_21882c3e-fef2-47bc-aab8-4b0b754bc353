<template>
  <div class="settings-page">
    <div class="page-header">
      <h2>系统设置</h2>
      <p class="page-desc">配置系统参数和设备连接信息</p>
    </div>

    <div class="settings-content">
      <el-row :gutter="20">
        <!-- 左侧设置菜单 -->
        <el-col :span="6">
          <el-card shadow="hover">
            <el-menu
              v-model:default-active="activeTab"
              class="settings-menu"
              @select="handleTabChange"
            >
              <el-menu-item index="system">
                <el-icon><Setting /></el-icon>
                <span>系统配置</span>
              </el-menu-item>
              <el-menu-item index="device">
                <el-icon><Monitor /></el-icon>
                <span>设备配置</span>
              </el-menu-item>
              <el-menu-item index="network">
                <el-icon><Connection /></el-icon>
                <span>网络配置</span>
              </el-menu-item>
              <el-menu-item index="appearance">
                <el-icon><Brush /></el-icon>
                <span>外观设置</span>
              </el-menu-item>
              <el-menu-item index="about">
                <el-icon><InfoFilled /></el-icon>
                <span>关于系统</span>
              </el-menu-item>
            </el-menu>
          </el-card>
        </el-col>

        <!-- 右侧设置内容 -->
        <el-col :span="18">
          <el-card shadow="hover" class="settings-panel">
            <!-- 系统配置 -->
            <div v-show="activeTab === 'system'" class="setting-section">
              <h3>系统配置</h3>
              <el-form :model="systemConfig" label-width="120px">
                <el-form-item label="项目名称">
                  <el-input v-model="systemConfig.projectName" />
                </el-form-item>
                <el-form-item label="站点名称">
                  <el-input v-model="systemConfig.stationName" />
                </el-form-item>
                <el-form-item label="设备编号">
                  <el-input v-model="systemConfig.equipmentId" />
                </el-form-item>
                <el-form-item label="重量阈值">
                  <el-input-number 
                    v-model="systemConfig.weightThreshold" 
                    :min="10" 
                    :max="100" 
                    :step="5"
                    controls-position="right"
                  />
                  <span class="form-tip">吨（超过此重量将提示警告）</span>
                </el-form-item>
                <el-form-item label="自动保存">
                  <el-switch v-model="systemConfig.autoSave" />
                  <span class="form-tip">自动保存车辆记录</span>
                </el-form-item>
              </el-form>
            </div>

            <!-- 设备配置 -->
            <div v-show="activeTab === 'device'" class="setting-section">
              <h3>设备配置</h3>
              <el-form :model="deviceConfig" label-width="120px">
                <el-divider content-position="left">摄像头设置</el-divider>
                <el-form-item label="主摄像头">
                  <el-input v-model="deviceConfig.camera.primary" />
                </el-form-item>
                <el-form-item label="副摄像头">
                  <el-input v-model="deviceConfig.camera.secondary" />
                </el-form-item>
                
                <el-divider content-position="left">称重设备</el-divider>
                <el-form-item label="串口">
                  <el-select v-model="deviceConfig.scale.port">
                    <el-option label="COM1" value="COM1" />
                    <el-option label="COM2" value="COM2" />
                    <el-option label="COM3" value="COM3" />
                    <el-option label="COM4" value="COM4" />
                  </el-select>
                </el-form-item>
                <el-form-item label="波特率">
                  <el-select v-model="deviceConfig.scale.baudRate">
                    <el-option label="9600" :value="9600" />
                    <el-option label="19200" :value="19200" />
                    <el-option label="38400" :value="38400" />
                    <el-option label="115200" :value="115200" />
                  </el-select>
                </el-form-item>
              </el-form>
            </div>

            <!-- 网络配置 -->
            <div v-show="activeTab === 'network'" class="setting-section">
              <h3>网络配置</h3>
              <el-form :model="networkConfig" label-width="120px">
                <el-divider content-position="left">API服务器</el-divider>
                <el-form-item label="服务器地址">
                  <el-input v-model="networkConfig.apiUrl" />
                </el-form-item>
                <el-form-item label="连接超时">
                  <el-input-number 
                    v-model="networkConfig.timeout" 
                    :min="5" 
                    :max="60" 
                    controls-position="right"
                  />
                  <span class="form-tip">秒</span>
                </el-form-item>

                <el-divider content-position="left">MQTT配置</el-divider>
                <el-form-item label="MQTT地址">
                  <el-input v-model="networkConfig.mqtt.host" />
                </el-form-item>
                <el-form-item label="端口">
                  <el-input-number 
                    v-model="networkConfig.mqtt.port" 
                    :min="1" 
                    :max="65535" 
                    controls-position="right"
                  />
                </el-form-item>
                <el-form-item label="用户名">
                  <el-input v-model="networkConfig.mqtt.username" />
                </el-form-item>
                <el-form-item label="密码">
                  <el-input v-model="networkConfig.mqtt.password" type="password" show-password />
                </el-form-item>
              </el-form>
            </div>

            <!-- 外观设置 -->
            <div v-show="activeTab === 'appearance'" class="setting-section">
              <h3>外观设置</h3>
              <el-form label-width="120px">
                <el-form-item label="主题模式">
                  <el-radio-group v-model="currentTheme" @change="handleThemeChange">
                    <el-radio value="light">亮色主题</el-radio>
                    <el-radio value="dark">暗色主题</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="侧边栏">
                  <el-switch 
                    v-model="sidebarCollapsed" 
                    @change="handleSidebarChange"
                    active-text="收起"
                    inactive-text="展开"
                  />
                </el-form-item>
                <el-form-item label="动画效果">
                  <el-switch v-model="animationEnabled" />
                  <span class="form-tip">启用页面切换动画</span>
                </el-form-item>
              </el-form>
            </div>

            <!-- 关于系统 -->
            <div v-show="activeTab === 'about'" class="setting-section">
              <h3>关于系统</h3>
              <div class="about-content">
                <div class="about-item">
                  <strong>系统名称：</strong>
                  <span>河北省采(弃)砂监管一体机系统</span>
                </div>
                <div class="about-item">
                  <strong>版本号：</strong>
                  <el-tag type="primary">v2.0.0</el-tag>
                </div>
                <div class="about-item">
                  <strong>开发者：</strong>
                  <span>仕伟</span>
                </div>
                <div class="about-item">
                  <strong>技术栈：</strong>
                  <el-tag style="margin: 2px;">Vue 3</el-tag>
                  <el-tag style="margin: 2px;">TypeScript</el-tag>
                  <el-tag style="margin: 2px;">Element Plus</el-tag>
                  <el-tag style="margin: 2px;">Vite</el-tag>
                </div>
                <div class="about-item">
                  <strong>构建时间：</strong>
                  <span>{{ buildTime }}</span>
                </div>
                <div class="about-item">
                  <strong>运行环境：</strong>
                  <span>{{ userAgent }}</span>
                </div>
              </div>
            </div>

            <!-- 操作按钮 -->
            <div class="setting-actions" v-if="activeTab !== 'about'">
              <el-button type="primary" @click="handleSave">保存设置</el-button>
              <el-button @click="handleReset">重置默认</el-button>
              <el-button type="info" @click="handleTest" v-if="activeTab === 'network'">测试连接</el-button>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
// 系统设置页面
// 作者：仕伟

import { useThemeStore } from '@/stores/theme'
import { useLayoutStore } from '@/stores/layout'
import dayjs from 'dayjs'

const themeStore = useThemeStore()
const layoutStore = useLayoutStore()

// 当前选中的标签页
const activeTab = ref('system')

// 主题相关
const currentTheme = computed({
  get: () => themeStore.theme,
  set: (value) => themeStore.setTheme(value)
})

// 侧边栏状态
const sidebarCollapsed = computed({
  get: () => layoutStore.sidebarCollapsed,
  set: (value) => layoutStore.setSidebarCollapsed(value)
})

// 动画效果
const animationEnabled = ref(true)

// 系统配置
const systemConfig = reactive({
  projectName: '河北省采(弃)砂监管项目',
  stationName: '采砂站点1号',
  equipmentId: 'EQ-001',
  weightThreshold: 40,
  autoSave: true
})

// 设备配置
const deviceConfig = reactive({
  camera: {
    primary: 'rtmp://127.0.0.1/live/camera1',
    secondary: 'rtmp://127.0.0.1/live/camera2'
  },
  scale: {
    port: 'COM3',
    baudRate: 9600
  }
})

// 网络配置
const networkConfig = reactive({
  apiUrl: 'http://127.0.0.1:8077',
  timeout: 10,
  mqtt: {
    host: '127.0.0.1',
    port: 8083,
    username: 'admin',
    password: 'admin123'
  }
})

// 构建时间和用户代理
const buildTime = dayjs().format('YYYY-MM-DD HH:mm:ss')
const userAgent = navigator.userAgent

// 处理标签页切换
const handleTabChange = (tab: string) => {
  activeTab.value = tab
}

// 处理主题切换
const handleThemeChange = (theme: string) => {
  ElMessage.success(`已切换到${theme === 'dark' ? '暗色' : '亮色'}主题`)
}

// 处理侧边栏切换
const handleSidebarChange = (collapsed: boolean) => {
  ElMessage.info(`侧边栏已${collapsed ? '收起' : '展开'}`)
}

// 保存设置
const handleSave = () => {
  ElMessage.success('设置已保存')
  // TODO: 实际保存设置到后端或本地存储
}

// 重置默认设置
const handleReset = () => {
  ElMessageBox.confirm('确定要重置为默认设置吗？', '确认重置', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // TODO: 重置设置
    ElMessage.success('已重置为默认设置')
  }).catch(() => {
    ElMessage.info('已取消重置')
  })
}

// 测试网络连接
const handleTest = async () => {
  const loading = ElLoading.service({
    lock: true,
    text: '正在测试网络连接...'
  })
  
  try {
    // TODO: 实际测试网络连接
    await new Promise(resolve => setTimeout(resolve, 2000))
    ElMessage.success('网络连接测试成功')
  } catch (error) {
    ElMessage.error('网络连接测试失败')
  } finally {
    loading.close()
  }
}
</script>

<style scoped lang="scss">
.settings-page {
  height: 100%;
  display: flex;
  flex-direction: column;

  .page-header {
    margin-bottom: var(--spacing-lg);

    h2 {
      margin: 0 0 var(--spacing-xs) 0;
      color: var(--text-primary);
      font-size: 24px;
      font-weight: 600;
    }

    .page-desc {
      margin: 0;
      color: var(--text-secondary);
      font-size: 14px;
    }
  }

  .settings-content {
    flex: 1;

    .settings-menu {
      border: none;

      :deep(.el-menu-item) {
        height: 48px;
        line-height: 48px;
      }
    }

    .settings-panel {
      min-height: 600px;

      .setting-section {
        h3 {
          margin: 0 0 var(--spacing-lg) 0;
          color: var(--text-primary);
          font-size: 18px;
          font-weight: 600;
          border-bottom: 2px solid var(--el-color-primary);
          padding-bottom: var(--spacing-sm);
        }

        .form-tip {
          margin-left: var(--spacing-sm);
          color: var(--text-secondary);
          font-size: 12px;
        }

        .about-content {
          .about-item {
            display: flex;
            align-items: center;
            padding: var(--spacing-md) 0;
            border-bottom: 1px solid var(--border-lighter);

            &:last-child {
              border-bottom: none;
            }

            strong {
              min-width: 100px;
              color: var(--text-primary);
            }

            span {
              color: var(--text-regular);
            }
          }
        }
      }

      .setting-actions {
        margin-top: var(--spacing-xl);
        padding-top: var(--spacing-lg);
        border-top: 1px solid var(--border-lighter);
        text-align: right;

        .el-button {
          margin-left: var(--spacing-sm);
        }
      }
    }
  }
}

// 移动端适配
@media (max-width: $breakpoint-md) {
  .settings-content {
    .el-col:first-child {
      margin-bottom: var(--spacing-lg);
    }
  }
}
</style>