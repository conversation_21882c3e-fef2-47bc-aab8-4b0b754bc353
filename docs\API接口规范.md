# 河北省采砂监管一体机系统 - 后台接口规范

## 📋 概述

本文档定义了前端与后台服务的完整接口约定，包括 WebSocket 实时消息协议和 HTTP RESTful API 接口。

## 🔌 WebSocket 接口规范

### 连接信息
- **WebSocket URL**: `ws://[服务器IP]:8080/ws`
- **协议**: WebSocket
- **数据格式**: JSON
- **心跳机制**: 30秒间隔

### 消息基础结构
```typescript
interface WebSocketMessage {
  type: MessageType              // 消息类型
  timestamp: number             // 时间戳（毫秒）
  data: any                    // 具体数据，根据消息类型变化
}

type MessageType = 
  | 'WEIGHT_DATA'              // 称重数据
  | 'PLATE_RECOGNITION'        // 车牌识别结果
  | 'DEVICE_STATUS'            // 设备状态更新
  | 'SYSTEM_NOTIFICATION'      // 系统通知
  | 'HEARTBEAT'               // 心跳消息
```

### 1. 称重数据 (WEIGHT_DATA)

**推送频率**: 实时（地磅数据变化时）
**数据结构**:
```json
{
  "type": "WEIGHT_DATA",
  "timestamp": 1757408036864,
  "data": {
    "value": 35.8,              // 重量值（吨）
    "unit": "吨",               // 单位
    "stable": true,             // 是否稳定
    "rawValue": 35786,          // 原始数值（kg）
    "deviceId": "scale_001",    // 设备ID
    "calibrationStatus": "normal" // 校准状态: normal/need_calibration/error
  }
}
```

### 2. 车牌识别结果 (PLATE_RECOGNITION)

**推送触发**: 检测到车辆进入识别区域
**数据结构**:
```json
{
  "type": "PLATE_RECOGNITION",
  "timestamp": 1757408036864,
  "data": {
    "plateNumber": "冀A12345",   // 识别的车牌号
    "confidence": 0.96,         // 识别置信度 (0-1)
    "position": "entrance",      // 位置: entrance/exit
    "imageUrl": "/images/plate_20241209_143056.jpg", // 抓拍图片URL
    "boundingBox": {            // 车牌位置框
      "x": 120,
      "y": 80,
      "width": 140,
      "height": 40
    },
    "cameraId": "camera_entrance", // 摄像头ID
    "vehicleType": "truck",     // 车辆类型: truck/car/bus
    "direction": "in"           // 方向: in/out
  }
}
```

### 3. 设备状态更新 (DEVICE_STATUS)

**推送频率**: 状态变化时或30秒心跳
**数据结构**:
```json
{
  "type": "DEVICE_STATUS",
  "timestamp": 1757408036864,
  "data": {
    "devices": [
      {
        "id": "camera_entrance",
        "name": "入口摄像头",
        "type": "camera",
        "status": "online",        // online/offline/error/maintenance
        "lastHeartbeat": 1757408036864,
        "errorMessage": null,      // 错误信息
        "properties": {
          "resolution": "1920x1080",
          "fps": 25,
          "nightVision": true
        }
      },
      {
        "id": "scale_001",
        "name": "地磅",
        "type": "scale",
        "status": "online",
        "lastHeartbeat": 1757408036864,
        "errorMessage": null,
        "properties": {
          "maxWeight": 80000,      // 最大称重(kg)
          "precision": 10,         // 精度(kg)
          "temperature": 25.5      // 工作温度
        }
      },
      {
        "id": "gate_entrance",
        "name": "入口道闸",
        "type": "gate",
        "status": "online",
        "lastHeartbeat": 1757408036864,
        "errorMessage": null,
        "properties": {
          "position": "closed",    // opened/closed/opening/closing
          "lockStatus": true       // 是否锁定
        }
      },
      {
        "id": "led_display",
        "name": "LED显示屏",
        "type": "led",
        "status": "online",
        "lastHeartbeat": 1757408036864,
        "errorMessage": null,
        "properties": {
          "brightness": 80,        // 亮度百分比
          "currentText": "请驶入称重区域"
        }
      }
    ]
  }
}
```

### 4. 系统通知 (SYSTEM_NOTIFICATION)

**推送触发**: 重要事件或异常情况
**数据结构**:
```json
{
  "type": "SYSTEM_NOTIFICATION",
  "timestamp": 1757408036864,
  "data": {
    "level": "warning",          // info/warning/error/critical
    "title": "超重预警",
    "message": "当前车辆重量超出限制，请检查",
    "category": "weight_alert",   // 通知分类
    "deviceId": "scale_001",     // 相关设备
    "autoClose": false,          // 是否自动关闭
    "actions": [                 // 可操作按钮
      {
        "text": "确认",
        "action": "confirm",
        "style": "primary"
      },
      {
        "text": "忽略",
        "action": "ignore",
        "style": "default"
      }
    ],
    "data": {                   // 额外数据
      "currentWeight": 85.6,
      "maxWeight": 80.0
    }
  }
}
```

---

## 🌐 HTTP API 接口规范

### 基础信息
- **Base URL**: `http://[服务器IP]:8080/api`
- **认证方式**: Bearer Token (如需要)
- **内容类型**: `application/json`
- **字符编码**: UTF-8

### 统一响应格式
```typescript
interface ApiResponse<T = any> {
  code: number                 // 状态码: 200成功，其他失败
  message: string              // 响应信息
  data: T                     // 响应数据
  timestamp: number           // 响应时间戳
}
```

### 1. 历史记录查询

**接口**: `GET /api/records`
**参数**:
```typescript
interface RecordsQuery {
  page?: number              // 页码，默认1
  size?: number              // 每页数量，默认20
  startTime?: string         // 开始时间 ISO8601
  endTime?: string           // 结束时间 ISO8601
  plateNumber?: string       // 车牌号筛选
  vehicleStatus?: 'IN' | 'OUT'  // 车辆状态筛选
  status?: 'normal' | 'overweight' | 'error'  // 称重状态筛选
}
```

**响应**:
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "records": [
      {
        "id": "rec_20241209_001",
        "plateNumber": "冀A12345",
        "weight": 45680,           // 重量(kg)
        "maxWeight": 80000,        // 最大限重(kg)
        "vehicleStatus": "IN",     // IN/OUT
        "status": "normal",        // normal/overweight/error
        "operator": "操作员001",
        "weighTime": "2024-12-09T14:30:56Z",
        "images": [
          "/images/weigh_20241209_143056_1.jpg",
          "/images/weigh_20241209_143056_2.jpg"
        ],
        "vehicleType": "truck",
        "grossWeight": 45680,      // 毛重
        "tareWeight": 12500,       // 皮重
        "netWeight": 33180,        // 净重
        "location": {
          "longitude": 114.5149,
          "latitude": 38.0428
        }
      }
    ],
    "total": 150,                // 总记录数
    "page": 1,                   // 当前页
    "size": 20,                  // 每页数量
    "totalPages": 8              // 总页数
  },
  "timestamp": 1757408036864
}
```

### 2. 设备控制

**道闸控制**: `POST /api/devices/gate/{gateId}/control`
```json
{
  "action": "open",              // open/close
  "reason": "正常过车",
  "operator": "system"
}
```

**LED显示**: `POST /api/devices/led/{ledId}/display`
```json
{
  "text": "欢迎使用采砂监管系统",
  "duration": 5000,              // 显示时长(ms)
  "brightness": 80,              // 亮度(0-100)
  "color": "#00FF00"            // 文字颜色
}
```

### 3. 数据统计

**实时统计**: `GET /api/statistics/realtime`
**响应**:
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "todayCount": 45,            // 今日过车数
    "todayWeight": 1256.8,       // 今日总重量(吨)
    "avgWeight": 27.9,           // 平均重量(吨)
    "overweightCount": 3,        // 超重次数
    "overweightRate": 6.7,       // 超重率(%)
    "deviceOnlineRate": 100,     // 设备在线率(%)
    "systemUptime": 86400        // 系统运行时间(秒)
  },
  "timestamp": 1757408036864
}
```

### 4. 系统配置

**获取配置**: `GET /api/config`
**更新配置**: `PUT /api/config`
```json
{
  "weightLimit": 80000,          // 限重(kg)
  "autoGate": true,              // 自动道闸
  "plateRecognition": {
    "enabled": true,
    "confidence": 0.8,           // 最低置信度
    "timeout": 10000            // 识别超时(ms)
  },
  "notifications": {
    "overweight": true,          // 超重通知
    "deviceOffline": true,       // 设备离线通知
    "systemError": true          // 系统错误通知
  }
}
```

---

## 🛠️ 后台实现建议

### 技术栈推荐
- **语言**: Go/Java/Python/Node.js
- **框架**: Go Gin/Spring Boot/FastAPI/Express
- **数据库**: MySQL/PostgreSQL + Redis
- **WebSocket**: Gorilla WebSocket/Socket.io
- **消息队列**: Redis/RabbitMQ/Kafka

### 核心模块设计

1. **WebSocket管理器**
   - 连接管理和心跳检测
   - 消息广播和订阅
   - 连接状态监控

2. **设备接口层**
   - 地磅数据采集
   - 摄像头视频流处理
   - 车牌识别算法集成
   - 道闸控制接口

3. **数据处理层**
   - 实时数据处理
   - 历史数据存储
   - 统计分析计算

4. **业务逻辑层**
   - 过车流程控制
   - 超重检测
   - 异常处理

### 部署架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端应用        │────│   Nginx代理      │────│   后台服务       │
│   (Vue3 + TS)   │    │   (负载均衡)     │    │   (REST + WS)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                      │
                       ┌─────────────────┐           │
                       │   Redis缓存      │───────────┤
                       │   (会话/消息)    │           │
                       └─────────────────┘           │
                                                      │
                       ┌─────────────────┐           │
                       │   MySQL数据库    │───────────┘
                       │   (历史数据)     │
                       └─────────────────┘
```