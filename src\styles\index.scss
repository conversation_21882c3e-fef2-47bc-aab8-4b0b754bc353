// 全局样式文件
// 作者：仕伟

@import './variables.scss';

// 全局重置样式
* {
  box-sizing: border-box;
}

html,
body {
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB',
    'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif, 'Apple Color Emoji',
    'Segoe UI Emoji', 'Segoe UI Symbol';
  font-size: 14px;
  line-height: 1.5;
  color: var(--text-primary);
  background-color: var(--bg-primary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  transition: var(--transition-base);
}

#app {
  height: 100%;
  width: 100%;
}

// 全局工具类
.flex {
  display: flex;

  &-center {
    align-items: center;
    justify-content: center;
  }

  &-between {
    justify-content: space-between;
  }

  &-around {
    justify-content: space-around;
  }

  &-column {
    flex-direction: column;
  }

  &-wrap {
    flex-wrap: wrap;
  }

  &-1 {
    flex: 1;
  }
}

.grid {
  display: grid;

  &-2 {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
  }

  &-3 {
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-md);
  }

  &-4 {
    grid-template-columns: repeat(4, 1fr);
    gap: var(--spacing-md);
  }
}

// 间距工具类
.m {
  &-xs { margin: var(--spacing-xs); }
  &-sm { margin: var(--spacing-sm); }
  &-md { margin: var(--spacing-md); }
  &-lg { margin: var(--spacing-lg); }
  &-xl { margin: var(--spacing-xl); }
}

.p {
  &-xs { padding: var(--spacing-xs); }
  &-sm { padding: var(--spacing-sm); }
  &-md { padding: var(--spacing-md); }
  &-lg { padding: var(--spacing-lg); }
  &-xl { padding: var(--spacing-xl); }
}

// 文本工具类
.text {
  &-center { text-align: center; }
  &-left { text-align: left; }
  &-right { text-align: right; }
  
  &-primary { color: var(--text-primary); }
  &-regular { color: var(--text-regular); }
  &-secondary { color: var(--text-secondary); }
  &-inverse { color: var(--text-inverse); }

  &-xs { font-size: 12px; }
  &-sm { font-size: 13px; }
  &-md { font-size: 14px; }
  &-lg { font-size: 16px; }
  &-xl { font-size: 18px; }
  &-xxl { font-size: 20px; }

  &-bold { font-weight: 600; }
  &-normal { font-weight: 400; }
}

// 状态色工具类
.status {
  &-online {
    color: var(--color-device-online);
  }

  &-offline {
    color: var(--color-device-offline);
  }

  &-warning {
    color: var(--color-device-warning);
  }
}

// 卡片样式
.card {
  background: var(--bg-card);
  border-radius: var(--border-radius-base);
  box-shadow: var(--shadow-card);
  border: 1px solid var(--border-base);
  transition: var(--transition-base);

  &:hover {
    box-shadow: var(--shadow-light);
    transform: translateY(-2px);
  }

  &-header {
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--border-light);
    font-weight: 600;
    color: var(--text-primary);
  }

  &-body {
    padding: var(--spacing-md);
  }
}

// 滚动条样式
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--border-base);
  border-radius: 4px;
  transition: var(--transition-base);

  &:hover {
    background: var(--text-secondary);
  }
}

// 响应式工具类
@media (max-width: $breakpoint-md) {
  .grid {
    &-2,
    &-3,
    &-4 {
      grid-template-columns: 1fr;
    }
  }

  .mobile-hidden {
    display: none !important;
  }
}

@media (min-width: $breakpoint-md) {
  .mobile-only {
    display: none !important;
  }
}

// 加载动画
.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid var(--border-light);
  border-radius: 50%;
  border-top-color: var(--el-color-primary);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

// 渐入动画
.fade-enter-active,
.fade-leave-active {
  transition: var(--transition-fade);
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

// 滑入动画
.slide-enter-active,
.slide-leave-active {
  transition: var(--transition-base);
}

.slide-enter-from {
  transform: translateX(-100%);
}

.slide-leave-to {
  transform: translateX(100%);
}