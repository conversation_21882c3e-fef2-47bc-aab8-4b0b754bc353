import { defineStore } from 'pinia'

// 主题管理 Store
// 作者：仕伟

export type ThemeMode = 'light' | 'dark'

interface ThemeState {
  theme: ThemeMode
}

export const useThemeStore = defineStore('theme', {
  state: (): ThemeState => ({
    theme: 'light'
  }),

  getters: {
    isDark: (state) => state.theme === 'dark'
  },

  actions: {
    // 设置主题
    setTheme(theme: ThemeMode) {
      this.theme = theme
      this.applyTheme()
      this.saveToStorage()
    },

    // 切换主题
    toggleTheme() {
      const newTheme = this.theme === 'light' ? 'dark' : 'light'
      this.setTheme(newTheme)
    },

    // 应用主题到DOM
    applyTheme() {
      const html = document.documentElement
      
      // 设置data-theme属性
      html.setAttribute('data-theme', this.theme)
      
      // 设置Element Plus的暗色模式类
      if (this.theme === 'dark') {
        html.classList.add('dark')
      } else {
        html.classList.remove('dark')
      }
    },

    // 保存到本地存储
    saveToStorage() {
      try {
        localStorage.setItem('sand-mining-theme', this.theme)
      } catch (error) {
        console.warn('无法保存主题设置到本地存储:', error)
      }
    },

    // 从本地存储读取
    loadFromStorage(): ThemeMode {
      try {
        const saved = localStorage.getItem('sand-mining-theme')
        if (saved && (saved === 'light' || saved === 'dark')) {
          return saved
        }
      } catch (error) {
        console.warn('无法从本地存储读取主题设置:', error)
      }
      
      // 默认根据系统偏好设置
      return this.getSystemPreference()
    },

    // 获取系统主题偏好
    getSystemPreference(): ThemeMode {
      if (typeof window !== 'undefined' && window.matchMedia) {
        return window.matchMedia('(prefers-color-scheme: dark)').matches 
          ? 'dark' 
          : 'light'
      }
      return 'light'
    },

    // 初始化主题
    initTheme() {
      const theme = this.loadFromStorage()
      this.setTheme(theme)
      
      // 监听系统主题变化
      if (typeof window !== 'undefined' && window.matchMedia) {
        const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
        mediaQuery.addEventListener('change', (e) => {
          // 只在用户没有手动设置主题时跟随系统
          const savedTheme = localStorage.getItem('sand-mining-theme')
          if (!savedTheme) {
            this.setTheme(e.matches ? 'dark' : 'light')
          }
        })
      }
    }
  }
})