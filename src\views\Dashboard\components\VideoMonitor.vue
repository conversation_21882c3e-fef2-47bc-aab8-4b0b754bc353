<template>
  <el-card class="video-monitor" shadow="hover">
    <template #header>
      <div class="card-header">
        <div class="header-left">
          <el-icon class="header-icon"><VideoCamera /></el-icon>
          <span class="header-title">实时监控</span>
        </div>
        <div class="header-right">
          <el-button-group>
            <el-tooltip content="重新连接" placement="top">
              <el-button 
                :icon="isPlaying ? 'VideoPause' : 'VideoPlay'" 
                size="small"
                @click="toggleVideo"
                :loading="videoLoading"
              />
            </el-tooltip>
            <el-tooltip content="全屏播放" placement="top">
              <el-button 
                icon="FullScreen" 
                size="small"
                @click="enterFullscreen"
              />
            </el-tooltip>
            <el-tooltip content="手动抓拍" placement="top">
              <el-button 
                icon="Camera" 
                size="small"
                @click="capturePhoto"
                :loading="captureLoading"
              />
            </el-tooltip>
          </el-button-group>
        </div>
      </div>
    </template>

    <div class="video-container" ref="videoContainer">
      <!-- 视频播放区域 -->
      <div class="video-player" v-show="isPlaying">
        <video
          ref="videoElement"
          class="video-element"
          :poster="videoPoster"
          @loadstart="handleVideoLoadStart"
          @loadeddata="handleVideoLoaded"
          @error="handleVideoError"
        />
        
        <!-- 录制指示器 -->
        <div class="recording-indicator" v-if="isRecording">
          <div class="rec-dot"></div>
          <span class="rec-text">录制中</span>
        </div>
      </div>

      <!-- 占位符 -->
      <div class="video-placeholder" v-show="!isPlaying">
        <div class="placeholder-content">
          <el-icon class="placeholder-icon"><VideoCamera /></el-icon>
          <p class="placeholder-text">视频未连接</p>
          <p class="placeholder-desc">点击播放按钮连接视频流</p>
        </div>
      </div>

      <!-- 加载状态 -->
      <div class="video-loading" v-if="videoLoading">
        <el-icon class="loading-icon is-loading"><Loading /></el-icon>
        <p>正在连接视频流...</p>
      </div>
    </div>

    <!-- 视频信息栏 -->
    <div class="video-info">
      <div class="info-item">
        <span class="info-label">分辨率:</span>
        <span class="info-value">{{ videoInfo.resolution }}</span>
      </div>
      <div class="info-item">
        <span class="info-label">帧率:</span>
        <span class="info-value">{{ videoInfo.fps }}</span>
      </div>
      <div class="info-item">
        <span class="info-label">码率:</span>
        <span class="info-value">{{ videoInfo.bitrate }}</span>
      </div>
      <div class="info-item">
        <span class="info-label">状态:</span>
        <el-tag :type="videoStatusType" size="small">{{ videoStatus }}</el-tag>
      </div>
    </div>
  </el-card>
</template>

<script setup lang="ts">
// 视频监控组件
// 作者：仕伟

import { deviceApi } from '@/services/api'
import { useFullscreen } from '@vueuse/core'

// 组件状态
const isPlaying = ref(false)
const videoLoading = ref(false)
const captureLoading = ref(false)
const isRecording = ref(false)

// DOM 引用
const videoElement = ref<HTMLVideoElement>()
const videoContainer = ref<HTMLElement>()

// 全屏功能
const { enter: enterFullscreen } = useFullscreen(videoContainer)

// 视频信息
const videoInfo = reactive({
  resolution: '1920x1080',
  fps: '25fps',
  bitrate: '2.1Mbps'
})

// 视频状态
const videoStatus = computed(() => {
  if (videoLoading.value) return '连接中'
  if (isPlaying.value) return '正常'
  return '未连接'
})

const videoStatusType = computed(() => {
  if (videoLoading.value) return 'warning'
  if (isPlaying.value) return 'success'
  return 'info'
})

// 视频封面图
const videoPoster = ref('/video-poster.jpg')

// 切换视频播放状态
const toggleVideo = async () => {
  if (isPlaying.value) {
    stopVideo()
  } else {
    await startVideo()
  }
}

// 开始播放视频
const startVideo = async () => {
  videoLoading.value = true
  
  try {
    // TODO: 实际对接时替换为真实的视频流地址
    // 这里可能是 FLV、HLS 或 WebRTC 流
    console.log('开始连接视频流...')
    
    // 模拟视频加载过程
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // TODO: 实际的视频流连接代码
    // if (videoElement.value) {
    //   const flvPlayer = flvjs.createPlayer({
    //     type: 'flv',
    //     url: 'ws://127.0.0.1:8080/live/stream.flv'
    //   })
    //   flvPlayer.attachMediaElement(videoElement.value)
    //   flvPlayer.load()
    //   flvPlayer.play()
    // }
    
    isPlaying.value = true
    ElMessage.success('视频连接成功')
  } catch (error) {
    console.error('视频连接失败:', error)
    ElMessage.error('视频连接失败，请检查网络连接')
  } finally {
    videoLoading.value = false
  }
}

// 停止播放视频
const stopVideo = () => {
  console.log('停止视频播放')
  
  // TODO: 清理视频流资源
  // if (flvPlayer) {
  //   flvPlayer.destroy()
  // }
  
  isPlaying.value = false
  ElMessage.info('视频已停止')
}

// 手动抓拍
const capturePhoto = async () => {
  if (!isPlaying.value) {
    ElMessage.warning('请先开启视频监控')
    return
  }

  captureLoading.value = true
  
  try {
    const result = await deviceApi.capturePhoto()
    if (result.success) {
      ElMessage.success('抓拍成功')
      // TODO: 可以显示抓拍的图片或保存到相册
      console.log('抓拍图片URL:', result.imageUrl)
    }
  } catch (error) {
    ElMessage.error('抓拍失败')
  } finally {
    captureLoading.value = false
  }
}

// 视频事件处理
const handleVideoLoadStart = () => {
  console.log('视频开始加载')
}

const handleVideoLoaded = () => {
  console.log('视频加载完成')
  isRecording.value = true
}

const handleVideoError = (event: Event) => {
  console.error('视频播放错误:', event)
  ElMessage.error('视频播放出现错误')
  isPlaying.value = false
  videoLoading.value = false
}

// 组件销毁时清理资源
onUnmounted(() => {
  stopVideo()
})
</script>

<style scoped lang="scss">
.video-monitor {
  height: 100%;
  
  :deep(.el-card__body) {
    height: calc(100% - 60px);
    padding: 0;
    display: flex;
    flex-direction: column;
  }

  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;

    .header-left {
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);

      .header-icon {
        color: var(--el-color-primary);
        font-size: 18px;
      }

      .header-title {
        font-size: 16px;
        font-weight: 600;
        color: var(--text-primary);
      }
    }

    .header-right {
      .el-button-group {
        .el-button {
          border: 1px solid var(--border-base);
        }
      }
    }
  }

  .video-container {
    flex: 1;
    position: relative;
    background: #000;
    border-radius: var(--border-radius-base);
    overflow: hidden;
    min-height: 300px;

    .video-player {
      width: 100%;
      height: 100%;
      position: relative;

      .video-element {
        width: 100%;
        height: 100%;
        object-fit: cover;
        background: #000;
      }

      .recording-indicator {
        position: absolute;
        top: var(--spacing-md);
        left: var(--spacing-md);
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
        background: rgba(0, 0, 0, 0.7);
        padding: var(--spacing-xs) var(--spacing-sm);
        border-radius: var(--border-radius-base);
        color: #fff;
        font-size: 12px;

        .rec-dot {
          width: 8px;
          height: 8px;
          background: #ff4d4f;
          border-radius: 50%;
          animation: pulse 1.5s infinite;
        }

        .rec-text {
          font-weight: 500;
        }
      }
    }

    .video-placeholder {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      background: var(--bg-secondary);

      .placeholder-content {
        text-align: center;
        color: var(--text-secondary);

        .placeholder-icon {
          font-size: 48px;
          color: var(--text-placeholder);
          margin-bottom: var(--spacing-md);
        }

        .placeholder-text {
          font-size: 16px;
          margin: 0 0 var(--spacing-xs) 0;
        }

        .placeholder-desc {
          font-size: 14px;
          margin: 0;
          opacity: 0.7;
        }
      }
    }

    .video-loading {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background: rgba(0, 0, 0, 0.8);
      color: #fff;
      z-index: 10;

      .loading-icon {
        font-size: 32px;
        margin-bottom: var(--spacing-md);
      }

      p {
        margin: 0;
        font-size: 14px;
      }
    }
  }

  .video-info {
    display: flex;
    justify-content: space-between;
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--bg-secondary);
    border-top: 1px solid var(--border-base);

    @media (max-width: $breakpoint-sm) {
      flex-wrap: wrap;
      gap: var(--spacing-xs);
    }

    .info-item {
      display: flex;
      align-items: center;
      gap: var(--spacing-xs);
      font-size: 12px;

      .info-label {
        color: var(--text-secondary);
      }

      .info-value {
        color: var(--text-primary);
        font-weight: 500;
      }
    }
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.3;
  }
}
</style>