<template>
  <div id="app" :data-theme="currentTheme">
    <!-- 全屏提示层 -->
    <div v-if="showFullscreenTip" class="fullscreen-tip">
      <div class="tip-content">
        <div class="tip-icon">🖥️</div>
        <div class="tip-text">正在进入全屏模式...</div>
        <div class="tip-subtitle">为获得最佳体验，系统将自动进入全屏</div>
      </div>
    </div>
    
    <router-view />
  </div>
</template>

<script setup lang="ts">
// 河北省采(弃)砂监管一体机系统 - 主应用组件
// 作者：仕伟

import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useThemeStore } from '@/stores/theme'
import { useForceFullscreen } from '@/composables/useForceFullscreen'

const themeStore = useThemeStore()
const currentTheme = computed(() => themeStore.theme)

// 全屏提示状态
const showFullscreenTip = ref(false)

// 使用强制全屏功能
const { initForceFullscreen, cleanupForceFullscreen, isFullscreen } = useForceFullscreen()

onMounted(() => {
  console.log('🚀 河北省采砂监管一体机系统启动 - 强制全屏模式')
  
  // 显示全屏提示
  showFullscreenTip.value = true
  
  // 初始化强制全屏功能
  initForceFullscreen()
  
  // 检查全屏状态并隐藏提示
  const checkFullscreen = () => {
    if (isFullscreen()) {
      showFullscreenTip.value = false
    } else {
      setTimeout(checkFullscreen, 500)
    }
  }
  
  // 3秒后强制隐藏提示（以防万一）
  setTimeout(() => {
    showFullscreenTip.value = false
  }, 3000)
  
  // 开始检查全屏状态
  setTimeout(checkFullscreen, 1000)
})

onUnmounted(() => {
  console.log('🛑 应用组件销毁，清理强制全屏功能')
  // 清理强制全屏功能
  cleanupForceFullscreen()
})
</script>

<style>
/* NProgress 样式自定义 */
#nprogress .bar {
  background: var(--el-color-primary) !important;
  height: 3px !important;
}

#nprogress .peg {
  box-shadow: 0 0 10px var(--el-color-primary), 0 0 5px var(--el-color-primary) !important;
}

#nprogress .spinner-icon {
  border-top-color: var(--el-color-primary) !important;
  border-left-color: var(--el-color-primary) !important;
}

/* 全屏提示样式 */
.fullscreen-tip {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #1a1f36 0%, #0f1419 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  animation: fadeIn 0.3s ease-in-out;
}

.tip-content {
  text-align: center;
  color: white;
  animation: slideUp 0.5s ease-out;
}

.tip-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  animation: pulse 2s infinite;
}

.tip-text {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #00D4FF;
}

.tip-subtitle {
  font-size: 1rem;
  color: #b0bec5;
  opacity: 0.8;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(30px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}
</style>