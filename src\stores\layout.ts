import { defineStore } from 'pinia'

// 布局管理 Store
// 作者：仕伟

interface LayoutState {
  sidebarCollapsed: boolean
  mobileSidebarOpen: boolean
  isMobile: boolean
}

export const useLayoutStore = defineStore('layout', {
  state: (): LayoutState => ({
    sidebarCollapsed: false,
    mobileSidebarOpen: false,
    isMobile: false
  }),

  actions: {
    // 切换侧边栏收起状态
    toggleSidebar() {
      if (this.isMobile) {
        this.mobileSidebarOpen = !this.mobileSidebarOpen
      } else {
        this.sidebarCollapsed = !this.sidebarCollapsed
        // 保存到本地存储
        this.saveSidebarState()
      }
    },

    // 设置侧边栏状态
    setSidebarCollapsed(collapsed: boolean) {
      this.sidebarCollapsed = collapsed
      this.saveSidebarState()
    },

    // 设置移动端侧边栏状态
    setMobileSidebarOpen(open: boolean) {
      this.mobileSidebarOpen = open
    },

    // 设置是否为移动端
    setIsMobile(isMobile: boolean) {
      this.isMobile = isMobile
      
      // 移动端时自动收起侧边栏
      if (isMobile) {
        this.mobileSidebarOpen = false
      }
    },

    // 保存侧边栏状态到本地存储
    saveSidebarState() {
      try {
        localStorage.setItem('sand-mining-sidebar-collapsed', String(this.sidebarCollapsed))
      } catch (error) {
        console.warn('无法保存侧边栏状态:', error)
      }
    },

    // 从本地存储加载侧边栏状态
    loadSidebarState() {
      try {
        const saved = localStorage.getItem('sand-mining-sidebar-collapsed')
        if (saved !== null) {
          this.sidebarCollapsed = saved === 'true'
        }
      } catch (error) {
        console.warn('无法从本地存储读取侧边栏状态:', error)
      }
    },

    // 初始化布局设置
    initLayout() {
      this.loadSidebarState()
      
      // 检测移动端
      this.checkIsMobile()
      
      // 监听窗口大小变化
      if (typeof window !== 'undefined') {
        const handleResize = () => {
          this.checkIsMobile()
        }
        
        window.addEventListener('resize', handleResize)
        
        // 清理函数
        return () => {
          window.removeEventListener('resize', handleResize)
        }
      }
    },

    // 检测是否为移动端
    checkIsMobile() {
      if (typeof window !== 'undefined') {
        this.setIsMobile(window.innerWidth < 768)
      }
    }
  }
})