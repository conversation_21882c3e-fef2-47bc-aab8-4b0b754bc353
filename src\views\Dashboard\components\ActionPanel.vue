<template>
  <el-card class="action-panel" shadow="hover">
    <template #header>
      <div class="card-header">
        <el-icon class="header-icon"><Operation /></el-icon>
        <span class="header-title">操作控制</span>
      </div>
    </template>

    <div class="action-content">
      <!-- 主要操作按钮 -->
      <div class="primary-actions">
        <el-button 
          type="primary" 
          size="large"
          @click="handleTriggerGate"
          :loading="gateLoading"
          :disabled="!canTriggerGate"
        >
          <el-icon><Unlock /></el-icon>
          {{ gateLoading ? '抬杆中...' : '触发抬杆' }}
        </el-button>
        
        <el-button 
          type="success" 
          size="large"
          @click="handleSubmitData"
          :loading="submitLoading"
          :disabled="!canSubmitData"
        >
          <el-icon><Upload /></el-icon>
          {{ submitLoading ? '提交中...' : '提交数据' }}
        </el-button>
      </div>

      <!-- 状态切换 -->
      <div class="status-switch">
        <div class="switch-label">车辆状态</div>
        <el-radio-group v-model="vehicleStatus" @change="handleStatusChange">
          <el-radio-button value="IN">入场</el-radio-button>
          <el-radio-button value="OUT">出场</el-radio-button>
        </el-radio-group>
      </div>

      <!-- 辅助操作 -->
      <div class="secondary-actions">
        <el-button @click="handleCapture" :loading="captureLoading">
          <el-icon><Camera /></el-icon>
          手动抓拍
        </el-button>
        
        <el-button @click="handleReset" type="info">
          <el-icon><RefreshLeft /></el-icon>
          重置状态
        </el-button>
      </div>

      <!-- 操作历史 -->
      <div class="operation-history">
        <div class="history-header">
          <span class="history-title">操作记录</span>
          <el-button link size="small" @click="clearHistory">清空</el-button>
        </div>
        <div class="history-list">
          <div 
            v-for="record in operationHistory" 
            :key="record.id"
            class="history-item"
          >
            <div class="history-time">{{ formatTime(record.timestamp) }}</div>
            <div class="history-action" :class="`action-${record.type}`">
              {{ record.description }}
            </div>
          </div>
          <div v-if="operationHistory.length === 0" class="history-empty">
            暂无操作记录
          </div>
        </div>
      </div>
    </div>
  </el-card>
</template>

<script setup lang="ts">
// 操作控制面板组件  
// 作者：仕伟

import { deviceApi, vehicleApi } from '@/services/api'
import dayjs from 'dayjs'

// 操作状态
const gateLoading = ref(false)
const submitLoading = ref(false)
const captureLoading = ref(false)

// 车辆状态
const vehicleStatus = ref<'IN' | 'OUT'>('IN')

// 操作历史记录
const operationHistory = ref<Array<{
  id: number
  timestamp: number
  type: 'gate' | 'submit' | 'capture' | 'reset'
  description: string
}>>([])

// 计算是否可以触发抬杆
const canTriggerGate = computed(() => {
  // TODO: 根据实际业务逻辑判断是否可以抬杆
  // 比如：车牌已确认 && 重量已确认 && 设备正常
  return true
})

// 计算是否可以提交数据
const canSubmitData = computed(() => {
  // TODO: 根据实际业务逻辑判断是否可以提交
  return true
})

// 触发抬杆
const handleTriggerGate = async () => {
  gateLoading.value = true
  
  try {
    await deviceApi.triggerGate()
    
    ElMessage.success('抬杆操作成功')
    addOperationRecord('gate', '触发道闸抬杆')
    
    // TODO: 可以在这里添加抬杆成功后的逻辑
    
  } catch (error) {
    ElMessage.error('抬杆操作失败')
    console.error('抬杆失败:', error)
  } finally {
    gateLoading.value = false
  }
}

// 提交数据
const handleSubmitData = async () => {
  submitLoading.value = true
  
  try {
    // TODO: 收集当前页面的所有数据
    const vehicleData = {
      plateNumber: 'TEST001', // 从车牌输入组件获取
      weight: 25.5, // 从称重组件获取
      status: vehicleStatus.value,
      timestamp: Date.now(),
      images: [] // 从抓拍获取
    }
    
    await vehicleApi.submitVehicleData(vehicleData)
    
    ElMessage.success('数据提交成功')
    addOperationRecord('submit', `提交${vehicleStatus.value === 'IN' ? '入场' : '出场'}数据`)
    
    // 提交成功后重置状态
    handleReset()
    
  } catch (error) {
    ElMessage.error('数据提交失败')
    console.error('提交失败:', error)
  } finally {
    submitLoading.value = false
  }
}

// 手动抓拍
const handleCapture = async () => {
  captureLoading.value = true
  
  try {
    const result = await deviceApi.capturePhoto()
    
    if (result.success) {
      ElMessage.success('抓拍成功')
      addOperationRecord('capture', '手动抓拍照片')
      
      // TODO: 可以在这里显示抓拍的图片
      console.log('抓拍图片URL:', result.imageUrl)
    }
  } catch (error) {
    ElMessage.error('抓拍失败')
  } finally {
    captureLoading.value = false
  }
}

// 重置状态
const handleReset = () => {
  ElMessageBox.confirm('确定要重置当前状态吗？', '确认重置', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // TODO: 重置各个组件的状态
    // 这里应该通过事件或者store来通知其他组件重置
    
    ElMessage.success('状态已重置')
    addOperationRecord('reset', '重置系统状态')
    
    console.log('重置系统状态')
  }).catch(() => {
    ElMessage.info('已取消重置')
  })
}

// 状态切换处理
const handleStatusChange = (status: 'IN' | 'OUT') => {
  ElMessage.info(`已切换到${status === 'IN' ? '入场' : '出场'}模式`)
  addOperationRecord('reset', `切换到${status === 'IN' ? '入场' : '出场'}模式`)
}

// 添加操作记录
const addOperationRecord = (type: string, description: string) => {
  const record = {
    id: Date.now(),
    timestamp: Date.now(),
    type: type as any,
    description
  }
  
  operationHistory.value.unshift(record)
  
  // 保持最近10条记录
  if (operationHistory.value.length > 10) {
    operationHistory.value = operationHistory.value.slice(0, 10)
  }
}

// 清空操作历史
const clearHistory = () => {
  operationHistory.value = []
  ElMessage.info('操作记录已清空')
}

// 格式化时间
const formatTime = (timestamp: number) => {
  return dayjs(timestamp).format('HH:mm:ss')
}

// 组件挂载时添加欢迎记录
onMounted(() => {
  addOperationRecord('reset', '系统启动完成')
})
</script>

<style scoped lang="scss">
.action-panel {
  height: 100%;
  
  :deep(.el-card__body) {
    height: calc(100% - 60px);
    padding: var(--spacing-md);
  }

  .card-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);

    .header-icon {
      color: var(--el-color-primary);
      font-size: 18px;
    }

    .header-title {
      font-size: 16px;
      font-weight: 600;
      color: var(--text-primary);
    }
  }

  .action-content {
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);

    .primary-actions {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-md);

      .el-button {
        width: 100%;
        height: 48px;
        font-size: 16px;
        font-weight: 600;

        .el-icon {
          margin-right: var(--spacing-sm);
        }
      }
    }

    .status-switch {
      .switch-label {
        font-size: 14px;
        font-weight: 500;
        color: var(--text-primary);
        margin-bottom: var(--spacing-sm);
      }

      .el-radio-group {
        width: 100%;

        :deep(.el-radio-button) {
          flex: 1;

          .el-radio-button__inner {
            width: 100%;
            text-align: center;
          }
        }
      }
    }

    .secondary-actions {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: var(--spacing-sm);

      .el-button {
        .el-icon {
          margin-right: var(--spacing-xs);
        }
      }
    }

    .operation-history {
      flex: 1;
      display: flex;
      flex-direction: column;
      border-top: 1px solid var(--border-base);
      padding-top: var(--spacing-md);

      .history-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: var(--spacing-sm);

        .history-title {
          font-size: 14px;
          font-weight: 500;
          color: var(--text-primary);
        }
      }

      .history-list {
        flex: 1;
        overflow-y: auto;

        .history-item {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: var(--spacing-xs) 0;
          border-bottom: 1px solid var(--border-lighter);

          &:last-child {
            border-bottom: none;
          }

          .history-time {
            font-size: 12px;
            color: var(--text-secondary);
            font-family: 'Courier New', monospace;
            min-width: 60px;
          }

          .history-action {
            flex: 1;
            font-size: 12px;
            margin-left: var(--spacing-sm);

            &.action-gate {
              color: var(--el-color-primary);
            }

            &.action-submit {
              color: var(--el-color-success);
            }

            &.action-capture {
              color: var(--el-color-warning);
            }

            &.action-reset {
              color: var(--text-regular);
            }
          }
        }

        .history-empty {
          text-align: center;
          color: var(--text-placeholder);
          font-size: 12px;
          padding: var(--spacing-lg) 0;
        }
      }
    }
  }
}
</style>