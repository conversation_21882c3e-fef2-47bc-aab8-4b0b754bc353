# 河北省采(弃)砂监管一体机系统 - 前端项目

> 现代化的Vue 3 + TypeScript工业控制界面，专为采砂监管业务设计  
> 作者：仕伟

## 🚀 项目特性

- ⚡️ **现代化技术栈**: Vue 3 + TypeScript + Vite
- 🎨 **精美UI设计**: Element Plus + 自定义工业控制主题
- 🌙 **主题切换**: 支持亮色/暗色主题，系统偏好自动适配
- 📱 **响应式布局**: 完美适配不同屏幕尺寸
- 🔧 **模块化架构**: 高度解耦，易于维护和扩展
- 📡 **实时通信**: WebSocket + HTTP混合通信模式
- 🎯 **类型安全**: 完整的TypeScript类型定义
- 🚦 **状态管理**: Pinia现代化状态管理

## 📋 功能模块

### 核心功能
- 🎥 **实时视频监控**: 双路视频流播放，支持FLV格式
- ⚖️ **智能称重系统**: 实时重量数据采集和趋势分析
- 🚗 **车牌识别**: 自动识别 + 手动输入双模式
- 🚪 **道闸控制**: 智能抬杆控制和状态反馈
- 📊 **数据管理**: 历史记录查询和数据导出

### 系统功能
- 🔒 **设备监控**: 实时设备状态监测和告警
- 📈 **数据可视化**: ECharts图表展示业务数据
- 🔄 **自动刷新**: 数据自动更新和错误恢复
- 💾 **本地缓存**: 用户设置和主题偏好保存

## 🛠️ 技术栈

- **框架**: Vue 3.4+ (Composition API)
- **语言**: TypeScript 5+
- **构建工具**: Vite 5+
- **UI库**: Element Plus 2.4+
- **状态管理**: Pinia 2.1+
- **路由**: Vue Router 4+
- **HTTP**: Axios
- **图表**: ECharts + Vue-ECharts
- **工具库**: VueUse, Day.js, CryptoJS

## 📦 开发环境

### 环境要求
- Node.js >= 16.0.0
- npm >= 7.0.0 或 yarn >= 1.22.0

### 快速开始

```bash
# 克隆项目
git clone <repository-url>

# 进入项目目录
cd sand-mining-dashboard

# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build

# 预览构建结果
npm run preview
```

### 开发脚本

```bash
# 开发服务器 (热重载)
npm run dev

# 类型检查
npm run type-check

# 代码规范检查
npm run lint

# 构建生产版本
npm run build
```

## 🏗️ 项目结构

```
src/
├── components/          # 全局组件
│   ├── StatusIndicator.vue    # 设备状态指示器
│   └── ThemeToggle.vue        # 主题切换组件
├── layout/             # 布局组件
│   ├── index.vue              # 主布局
│   └── components/            # 布局子组件
├── views/              # 页面组件
│   ├── Dashboard/             # 主控制台
│   ├── Monitor/               # 实时监控
│   ├── History/               # 历史记录
│   └── Loading/               # 启动页面
├── stores/             # Pinia状态管理
│   ├── theme.ts               # 主题状态
│   ├── layout.ts              # 布局状态
│   └── device.ts              # 设备状态
├── services/           # API服务层
│   ├── api.ts                 # HTTP接口
│   └── websocket.ts           # WebSocket服务
├── router/             # 路由配置
├── styles/             # 样式文件
│   ├── variables.scss         # SCSS变量
│   └── index.scss             # 全局样式
└── utils/              # 工具函数
```

## 🎨 UI设计理念

### 设计原则
- **简洁明了**: 突出核心功能，减少视觉噪音
- **工业美学**: 深色主题为主，科技感配色
- **状态驱动**: 界面自动响应数据变化
- **操作友好**: 大按钮设计，适合工业环境

### 色彩体系
- **主色调**: #409EFF (Element Plus 蓝)
- **成功色**: #67C23A
- **警告色**: #E6A23C  
- **危险色**: #F56C6C
- **设备在线**: #52C41A
- **设备离线**: #FF4D4F

### 响应式设计
- **桌面端**: >= 1200px (主要适配)
- **平板端**: 768px - 1199px
- **手机端**: < 768px (基础支持)

## 🔌 接口对接说明

### HTTP接口
所有HTTP接口都在 `src/services/api.ts` 中定义，目前使用模拟数据，实际对接时只需要：

1. 取消注释真实接口调用代码
2. 删除模拟数据返回代码
3. 调整接口地址和参数格式

### WebSocket连接
WebSocket服务在 `src/services/websocket.ts` 中实现，支持：

- 自动重连机制
- 消息类型订阅
- 连接状态管理
- 模拟数据推送（开发阶段）

### 环境配置
在 `.env.development` 中配置：

```bash
# API接口地址
VITE_API_URL=http://127.0.0.1:8077

# WebSocket地址  
VITE_WS_URL=ws://127.0.0.1:8080/ws

# MQTT配置
VITE_MQTT_URL=ws://127.0.0.1:8083/mqtt
```

## 🔧 配置说明

### Vite配置
- 自动导入 Vue API 和 Element Plus 组件
- SCSS变量全局引入
- 代理配置支持后端接口调试
- 构建优化和代码分割

### TypeScript配置
- 严格模式启用
- 路径别名支持 (@/ 指向 src/)
- Element Plus 全局类型支持

### 样式架构
- CSS自定义属性支持主题切换
- SCSS变量统一管理设计令牌
- 工具类提供常用样式
- 响应式断点统一管理

## 📱 主题系统

### 亮色主题
- 白色背景为主
- 浅色卡片和面板
- 深色文字确保可读性

### 暗色主题  
- 深色背景减少眼部疲劳
- 高对比度提升专业感
- 适合长时间监控使用

### 主题切换
- 用户手动切换
- 系统偏好自动适配
- 设置本地存储记忆

## 🚨 开发注意事项

### 代码规范
- 组件名使用 PascalCase
- 文件名使用 kebab-case  
- 所有组件都要添加作者信息注释
- 使用 TypeScript 严格模式

### 性能优化
- 路由懒加载
- 组件按需导入
- 图片资源压缩
- 长列表虚拟滚动

### 错误处理
- 统一的错误边界
- 友好的错误提示
- 网络异常重试机制
- 日志记录和监控

## 🤝 贡献指南

1. Fork 本项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 👨‍💻 作者

**仕伟** - *初始开发* - [GitHub](https://github.com/shiwei)

---

> 💡 **提示**: 这是一个专业的工业控制界面项目，注重稳定性和用户体验。如有问题请提交 Issue 或联系开发者。