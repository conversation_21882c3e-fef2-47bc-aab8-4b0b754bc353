// WebSocket 连接管理服务
// 作者：仕伟

export type MessageType = 'WEIGHT_DATA' | 'PLATE_RECOGNITION' | 'DEVICE_STATUS' | 'SYSTEM_NOTIFICATION'

export interface WebSocketMessage {
  type: MessageType
  timestamp: number
  data: any
}

export interface WebSocketConfig {
  url: string
  reconnectInterval: number
  maxReconnectAttempts: number
}

export class WebSocketService {
  private ws: WebSocket | null = null
  private config: WebSocketConfig
  private isConnected = false
  private reconnectAttempts = 0
  private messageHandlers = new Map<MessageType, Array<(data: any) => void>>()
  private reconnectTimer: number | null = null

  constructor(config: Partial<WebSocketConfig> = {}) {
    this.config = {
      url: 'ws://127.0.0.1:8080/ws',
      reconnectInterval: 3000,
      maxReconnectAttempts: 10,
      ...config
    }
  }

  // 连接 WebSocket
  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        // TODO: 实际对接时替换为真实 WebSocket 地址
        // this.ws = new WebSocket(this.config.url)
        
        // 模拟 WebSocket 连接
        console.log('尝试连接 WebSocket:', this.config.url)
        
        // 模拟连接成功
        setTimeout(() => {
          this.isConnected = true
          this.reconnectAttempts = 0
          console.log('WebSocket 连接成功')
          resolve()
          
          // 启动模拟数据推送
          this.startMockDataPush()
        }, 500)

        // 真实 WebSocket 连接代码:
        /*
        this.ws.onopen = () => {
          this.isConnected = true
          this.reconnectAttempts = 0
          console.log('WebSocket 连接成功')
          resolve()
        }

        this.ws.onmessage = (event) => {
          this.handleMessage(event.data)
        }

        this.ws.onclose = () => {
          this.isConnected = false
          console.log('WebSocket 连接关闭')
          this.attemptReconnect()
        }

        this.ws.onerror = (error) => {
          console.error('WebSocket 连接错误:', error)
          reject(error)
        }
        */
      } catch (error) {
        console.error('WebSocket 连接失败:', error)
        reject(error)
      }
    })
  }

  // 断开连接
  disconnect() {
    if (this.ws) {
      this.ws.close()
      this.ws = null
    }
    
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }
    
    this.isConnected = false
    console.log('WebSocket 已断开连接')
  }

  // 发送消息
  send(message: WebSocketMessage) {
    if (this.isConnected && this.ws) {
      this.ws.send(JSON.stringify(message))
    } else {
      console.warn('WebSocket 未连接，无法发送消息')
    }
  }

  // 订阅消息类型
  subscribe(type: MessageType, handler: (data: any) => void) {
    if (!this.messageHandlers.has(type)) {
      this.messageHandlers.set(type, [])
    }
    this.messageHandlers.get(type)!.push(handler)
  }

  // 取消订阅
  unsubscribe(type: MessageType, handler: (data: any) => void) {
    const handlers = this.messageHandlers.get(type)
    if (handlers) {
      const index = handlers.indexOf(handler)
      if (index !== -1) {
        handlers.splice(index, 1)
      }
    }
  }

  // 处理收到的消息
  private handleMessage(data: string) {
    try {
      const message: WebSocketMessage = JSON.parse(data)
      console.log('收到 WebSocket 消息:', message)
      
      const handlers = this.messageHandlers.get(message.type)
      if (handlers) {
        handlers.forEach(handler => handler(message.data))
      }
    } catch (error) {
      console.error('解析 WebSocket 消息失败:', error)
    }
  }

  // 尝试重连
  private attemptReconnect() {
    if (this.reconnectAttempts >= this.config.maxReconnectAttempts) {
      console.error('WebSocket 重连次数已达上限，停止重连')
      return
    }

    this.reconnectAttempts++
    console.log(`WebSocket 重连尝试 ${this.reconnectAttempts}/${this.config.maxReconnectAttempts}`)

    this.reconnectTimer = window.setTimeout(() => {
      this.connect().catch(() => {
        // 重连失败，继续尝试
        this.attemptReconnect()
      })
    }, this.config.reconnectInterval)
  }

  // 获取连接状态
  isWebSocketConnected(): boolean {
    return this.isConnected
  }

  // 模拟数据推送（用于开发阶段）
  private startMockDataPush() {
    // 模拟称重数据推送
    setInterval(() => {
      if (this.isConnected) {
        const weightData = {
          value: 20 + Math.random() * 30,
          unit: '吨',
          stable: Math.random() > 0.2,
          timestamp: Date.now()
        }
        
        this.handleMessage(JSON.stringify({
          type: 'WEIGHT_DATA',
          timestamp: Date.now(),
          data: weightData
        }))
      }
    }, 2000) // 每2秒推送一次称重数据

    // 模拟车牌识别结果推送
    const plateNumbers = ['冀A12345', '冀B67890', '冀C11111', '冀D22222']
    setInterval(() => {
      if (this.isConnected && Math.random() > 0.8) { // 20% 概率推送车牌识别
        const plateResult = {
          plateNumber: plateNumbers[Math.floor(Math.random() * plateNumbers.length)],
          confidence: 0.85 + Math.random() * 0.14,
          timestamp: Date.now(),
          imageUrl: `/mock-plate-${Date.now()}.jpg`
        }
        
        this.handleMessage(JSON.stringify({
          type: 'PLATE_RECOGNITION',
          timestamp: Date.now(),
          data: plateResult
        }))
      }
    }, 5000) // 每5秒检查一次

    // 模拟设备状态变化
    setInterval(() => {
      if (this.isConnected && Math.random() > 0.9) { // 10% 概率设备状态变化
        const deviceStatus = {
          camera: Math.random() > 0.1 ? 'online' : 'offline',
          scale: Math.random() > 0.05 ? 'online' : 'offline',
          gate: Math.random() > 0.05 ? 'online' : 'offline'
        }
        
        this.handleMessage(JSON.stringify({
          type: 'DEVICE_STATUS',
          timestamp: Date.now(),
          data: deviceStatus
        }))
      }
    }, 10000) // 每10秒检查一次
  }
}

// 创建全局 WebSocket 服务实例
export const wsService = new WebSocketService()

// 导出用于 Vue 组件的组合式函数
export function useWebSocket() {
  const isConnected = ref(false)
  
  // 连接状态监听
  const updateConnectionStatus = () => {
    isConnected.value = wsService.isWebSocketConnected()
  }
  
  // 连接 WebSocket
  const connect = async () => {
    try {
      await wsService.connect()
      updateConnectionStatus()
    } catch (error) {
      console.error('WebSocket 连接失败:', error)
    }
  }
  
  // 断开连接
  const disconnect = () => {
    wsService.disconnect()
    updateConnectionStatus()
  }
  
  // 订阅消息
  const subscribe = (type: MessageType, handler: (data: any) => void) => {
    wsService.subscribe(type, handler)
  }
  
  // 取消订阅
  const unsubscribe = (type: MessageType, handler: (data: any) => void) => {
    wsService.unsubscribe(type, handler)
  }
  
  // 发送消息
  const send = (type: MessageType, data: any) => {
    wsService.send({
      type,
      timestamp: Date.now(),
      data
    })
  }
  
  // 定期更新连接状态
  onMounted(() => {
    const timer = setInterval(updateConnectionStatus, 1000)
    onUnmounted(() => clearInterval(timer))
  })
  
  return {
    isConnected: readonly(isConnected),
    connect,
    disconnect,
    subscribe,
    unsubscribe,
    send
  }
}