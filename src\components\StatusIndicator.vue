<template>
  <div class="status-indicator" :class="statusClass">
    <div class="indicator-content">
      <div class="status-dot">
        <div v-if="status === 'online'" class="pulse-ring"></div>
      </div>
      <el-icon class="status-icon">
        <component :is="icon" />
      </el-icon>
    </div>
    <div class="status-info">
      <div class="status-label">{{ label }}</div>
      <div class="status-text">{{ statusText }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 设备状态指示器组件
// 作者：仕伟

interface Props {
  status: 'online' | 'offline' | 'warning' | 'error'
  label: string
  icon: string
  showPulse?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showPulse: true
})

// 状态样式类
const statusClass = computed(() => [`status-${props.status}`])

// 状态文本
const statusText = computed(() => {
  const statusMap = {
    online: '在线',
    offline: '离线',
    warning: '警告',
    error: '错误'
  }
  return statusMap[props.status]
})
</script>

<style scoped lang="scss">
.status-indicator {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius-base);
  background: var(--bg-card);
  border: 1px solid var(--border-base);
  transition: var(--transition-base);
  min-width: 120px;

  &:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-light);
  }

  .indicator-content {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;

    .status-dot {
      position: absolute;
      width: 8px;
      height: 8px;
      border-radius: 50%;
      top: 2px;
      right: 2px;
      z-index: 2;

      .pulse-ring {
        position: absolute;
        width: 16px;
        height: 16px;
        border: 2px solid transparent;
        border-radius: 50%;
        top: -4px;
        left: -4px;
        animation: pulse 2s infinite;
      }
    }

    .status-icon {
      font-size: 16px;
      z-index: 1;
    }
  }

  .status-info {
    flex: 1;
    min-width: 0;

    .status-label {
      font-size: 12px;
      color: var(--text-secondary);
      line-height: 1;
      margin-bottom: 2px;
    }

    .status-text {
      font-size: 13px;
      font-weight: 500;
      line-height: 1;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  // 在线状态
  &.status-online {
    .status-dot {
      background-color: var(--color-device-online);
      box-shadow: 0 0 6px rgba(82, 196, 26, 0.6);

      .pulse-ring {
        border-color: var(--color-device-online);
      }
    }

    .status-icon {
      color: var(--color-device-online);
    }

    .status-text {
      color: var(--color-device-online);
    }
  }

  // 离线状态
  &.status-offline {
    .status-dot {
      background-color: var(--color-device-offline);
      box-shadow: 0 0 6px rgba(255, 77, 79, 0.6);
    }

    .status-icon {
      color: var(--color-device-offline);
    }

    .status-text {
      color: var(--color-device-offline);
    }
  }

  // 警告状态
  &.status-warning {
    .status-dot {
      background-color: var(--color-device-warning);
      box-shadow: 0 0 6px rgba(250, 173, 20, 0.6);
    }

    .status-icon {
      color: var(--color-device-warning);
    }

    .status-text {
      color: var(--color-device-warning);
    }
  }

  // 错误状态
  &.status-error {
    .status-dot {
      background-color: var(--el-color-danger);
      box-shadow: 0 0 6px rgba(245, 108, 108, 0.6);
    }

    .status-icon {
      color: var(--el-color-danger);
    }

    .status-text {
      color: var(--el-color-danger);
    }
  }
}

@keyframes pulse {
  0% {
    transform: scale(0.95);
    opacity: 1;
  }
  70% {
    transform: scale(1.8);
    opacity: 0;
  }
  100% {
    transform: scale(2);
    opacity: 0;
  }
}

// 移动端适配
@media (max-width: $breakpoint-sm) {
  .status-indicator {
    min-width: 80px;
    padding: var(--spacing-xs) var(--spacing-sm);

    .status-info {
      .status-label {
        font-size: 10px;
      }

      .status-text {
        font-size: 11px;
      }
    }
  }
}
</style>