<template>
  <div class="layout-container">
    <!-- 顶部导航栏 -->
    <HeaderBar />
    
    <!-- 主内容区 -->
    <div class="layout-main">
      <!-- 侧边栏 -->
      <SideBar 
        :collapsed="sidebarCollapsed" 
        @toggle="handleSidebarToggle" 
      />
      
      <!-- 内容区域 -->
      <div class="layout-content" :class="{ 'sidebar-collapsed': sidebarCollapsed }">
        <div class="content-wrapper">
          <Transition name="fade" mode="out-in">
            <router-view />
          </Transition>
        </div>
      </div>
    </div>

    <!-- 全局通知 -->
    <GlobalNotification />
  </div>
</template>

<script setup lang="ts">
// 河北省采(弃)砂监管一体机系统 - 主布局组件
// 作者：仕伟

import HeaderBar from './components/HeaderBar.vue'
import SideBar from './components/SideBar.vue'
import GlobalNotification from './components/GlobalNotification.vue'
import { useLayoutStore } from '@/stores/layout'

const layoutStore = useLayoutStore()

const sidebarCollapsed = computed(() => layoutStore.sidebarCollapsed)

const handleSidebarToggle = () => {
  layoutStore.toggleSidebar()
}
</script>

<style scoped lang="scss">
.layout-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--bg-primary);
}

.layout-main {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.layout-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  transition: var(--transition-base);
  margin-left: 200px;

  &.sidebar-collapsed {
    margin-left: 64px;
  }

  @media (max-width: $breakpoint-md) {
    margin-left: 0;
  }
}

.content-wrapper {
  flex: 1;
  padding: var(--spacing-lg);
  overflow-y: auto;
  background: var(--bg-secondary);

  @media (max-width: $breakpoint-md) {
    padding: var(--spacing-md);
  }
}

// 页面切换动画
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>