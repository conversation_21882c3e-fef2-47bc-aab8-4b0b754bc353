<template>
  <div class="boot-system" :class="{ 'boot-completed': bootCompleted }">
    <!-- 系统启动画面 -->
    <div class="boot-screen" v-show="!bootCompleted">
      <!-- BIOS风格启动 -->
      <div class="bios-section" v-show="currentPhase === 'bios'">
        <div class="bios-header">
          <h1>采砂监管一体机系统</h1>
          <p>Sand Mining Terminal System v2.0.0</p>
          <div class="bios-info">
            <div class="system-info">
              <p>设备型号: SM-Terminal-2024</p>
              <p>系统时间: {{ systemTime }}</p>
            </div>
          </div>
        </div>
        
        <div class="hardware-check">
          <h3>硬件自检中...</h3>
          <div class="check-items">
            <div 
              v-for="item in hardwareItems" 
              :key="item.name"
              class="check-item"
              :class="item.status"
            >
              <span class="item-name">{{ item.name }}</span>
              <span class="item-dots">{{ '.'.repeat(50 - item.name.length) }}</span>
              <span class="item-status">{{ item.statusText }}</span>
            </div>
          </div>
        </div>

        <div class="boot-progress">
          <div class="progress-text">系统启动进度: {{ bootProgress }}%</div>
          <div class="progress-bar">
            <div class="progress-fill" :style="{ width: bootProgress + '%' }"></div>
          </div>
        </div>
      </div>

      <!-- 系统加载阶段 -->
      <div class="loading-section" v-show="currentPhase === 'loading'">
        <div class="logo-animation">
          <img src="/logo.svg" alt="System Logo" class="rotating-logo" />
          <h2>河北省采砂监管一体机系统</h2>
          <p class="version">Version 2.0.0 Build {{ buildNumber }}</p>
        </div>

        <div class="loading-steps">
          <div 
            v-for="(step, index) in loadingSteps"
            :key="step.id"
            class="loading-step"
            :class="{ 
              'completed': step.completed,
              'current': step.current,
              'error': step.error
            }"
          >
            <div class="step-icon">
              <el-icon v-if="step.completed && !step.error">
                <CircleCheckFilled />
              </el-icon>
              <el-icon v-else-if="step.error">
                <CircleCloseFilled />
              </el-icon>
              <el-icon v-else-if="step.current" class="loading-icon">
                <Loading />
              </el-icon>
              <div v-else class="waiting-dot">●</div>
            </div>
            <span class="step-text">{{ step.text }}</span>
            <div v-if="step.current" class="step-progress">
              <div class="progress-dots">
                <span v-for="n in 3" :key="n" class="dot"></span>
              </div>
            </div>
          </div>
        </div>

        <div class="system-status">
          <div class="status-item">
            <strong>设备状态:</strong> {{ deviceStatusText }}
          </div>
          <div class="status-item">
            <strong>网络连接:</strong> {{ networkStatusText }}
          </div>
          <div class="status-item">
            <strong>数据库:</strong> {{ databaseStatusText }}
          </div>
        </div>
      </div>

      <!-- 准备就绪阶段 -->
      <div class="ready-section" v-show="currentPhase === 'ready'">
        <div class="ready-animation">
          <el-icon class="success-icon">
            <CircleCheckFilled />
          </el-icon>
          <h2>系统准备就绪</h2>
          <p>所有组件已成功启动</p>
        </div>

        <div class="ready-info">
          <div class="info-grid">
            <div class="info-item">
              <strong>启动时间</strong>
              <span>{{ bootTime }}秒</span>
            </div>
            <div class="info-item">
              <strong>设备总数</strong>
              <span>3台</span>
            </div>
            <div class="info-item">
              <strong>在线设备</strong>
              <span>3台</span>
            </div>
            <div class="info-item">
              <strong>系统状态</strong>
              <span class="status-ok">正常</span>
            </div>
          </div>
        </div>

        <div class="ready-actions">
          <el-button 
            type="primary" 
            size="large"
            @click="enterSystem"
            :loading="entering"
          >
            <el-icon><Right /></el-icon>
            进入系统
          </el-button>
        </div>

        <div class="auto-enter">
          <p>{{ autoEnterCountdown }}秒后自动进入系统</p>
        </div>
      </div>
    </div>

    <!-- 系统启动完成提示 -->
    <div v-if="bootCompleted" class="boot-complete">
      <div class="complete-animation">
        <el-icon class="success-icon">
          <CircleCheckFilled />
        </el-icon>
        <h2>系统启动完成</h2>
        <p>正在进入一体机界面...</p>
        <div class="loading-dots">
          <span v-for="n in 3" :key="n" class="dot"></span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 一体机启动系统
// 作者：仕伟

import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import dayjs from 'dayjs'
import { 
  CircleCheckFilled, 
  CircleCloseFilled, 
  Loading, 
  Right 
} from '@element-plus/icons-vue'

const router = useRouter()

// 启动阶段
const currentPhase = ref<'bios' | 'loading' | 'ready'>('bios')
const bootCompleted = ref(false)
const bootProgress = ref(0)
const entering = ref(false)
const autoEnterCountdown = ref(2)

// 系统信息
const serialNumber = ref(`SM${Date.now().toString().slice(-8)}`)
const systemTime = ref('')
const buildNumber = ref(Date.now().toString().slice(-6))
const bootStartTime = ref(Date.now())

// 硬件检查项目
const hardwareItems = ref([
  { name: 'CPU & 内存', status: 'checking', statusText: '检测中...' },
  { name: '网络连接', status: 'checking', statusText: '检测中...' },
  { name: '摄像头设备', status: 'checking', statusText: '检测中...' },
  { name: '称重传感器', status: 'checking', statusText: '检测中...' },
  { name: '道闸控制器', status: 'checking', statusText: '检测中...' }
])

// 系统加载步骤
const loadingSteps = ref([
  { id: 1, text: '初始化系统组件', completed: false, current: false, error: false },
  { id: 2, text: '连接数据库服务', completed: false, current: false, error: false },
  { id: 3, text: '启动设备驱动', completed: false, current: false, error: false },
  { id: 4, text: '检测硬件连接', completed: false, current: false, error: false },
  { id: 5, text: '加载用户界面', completed: false, current: false, error: false }
])

// 状态文本
const deviceStatusText = computed(() => {
  const completedSteps = loadingSteps.value.filter(s => s.completed).length
  if (completedSteps < 5) return '初始化中...'
  if (completedSteps < 8) return '设备连接中...'
  return '设备就绪'
})

const networkStatusText = computed(() => {
  const networkStep = loadingSteps.value.find(s => s.id === 4)
  if (networkStep?.completed) return '已连接'
  if (networkStep?.current) return '连接中...'
  return '等待连接'
})

const databaseStatusText = computed(() => {
  const dbStep = loadingSteps.value.find(s => s.id === 3)
  if (dbStep?.completed) return '已连接'
  if (dbStep?.current) return '连接中...'
  return '等待连接'
})

const bootTime = computed(() => {
  return ((Date.now() - bootStartTime.value) / 1000).toFixed(1)
})

// 更新系统时间
const updateSystemTime = () => {
  systemTime.value = dayjs().format('YYYY-MM-DD HH:mm:ss')
}

// 硬件检查动画
const startHardwareCheck = async () => {
  for (let i = 0; i < hardwareItems.value.length; i++) {
    const item = hardwareItems.value[i]
    item.status = 'checking'
    item.statusText = '检测中...'
    
    // 模拟检测时间
    await new Promise(resolve => setTimeout(resolve, 100 + Math.random() * 200))
    
    // 随机决定检测结果（95%成功率）
    const success = Math.random() > 0.05
    item.status = success ? 'ok' : 'error'
    item.statusText = success ? '正常' : '异常'
    
    // 更新进度
    bootProgress.value = Math.floor(((i + 1) / hardwareItems.value.length) * 100)
  }
}

// 系统加载步骤
const startLoadingSequence = async () => {
  for (let i = 0; i < loadingSteps.value.length; i++) {
    const step = loadingSteps.value[i]
    
    // 设置当前步骤
    step.current = true
    
    // 模拟加载时间
    await new Promise(resolve => {
      const time = 200 + Math.random() * 300
      setTimeout(resolve, time)
    })
    
    // 完成当前步骤
    step.current = false
    step.completed = true
    
    // 小概率出现错误（用于演示）
    if (Math.random() < 0.02) {
      step.error = true
      step.completed = false
      
      // 错误恢复
      setTimeout(() => {
        step.error = false
        step.completed = true
      }, 1000)
    }
  }
}

// 自动倒计时
const startAutoEnterCountdown = () => {
  const timer = setInterval(() => {
    autoEnterCountdown.value--
    if (autoEnterCountdown.value <= 0) {
      clearInterval(timer)
      enterSystem()
    }
  }, 1000)
}

// 进入系统
const enterSystem = () => {
  entering.value = true
  
  setTimeout(() => {
    bootCompleted.value = true
    // 延迟跳转到终端界面
    setTimeout(() => {
      router.push('/terminal')
    }, 800)
  }, 500)
}

// 启动序列
const startBootSequence = async () => {
  // Phase 1: BIOS自检
  currentPhase.value = 'bios'
  await startHardwareCheck()
  
  // 等待一下显示完成状态
  await new Promise(resolve => setTimeout(resolve, 300))
  
  // Phase 2: 系统加载
  currentPhase.value = 'loading'
  await startLoadingSequence()
  
  // Phase 3: 准备就绪
  currentPhase.value = 'ready'
  startAutoEnterCountdown()
}

// 生命周期
onMounted(() => {
  updateSystemTime()
  setInterval(updateSystemTime, 1000)
  
  // 开始启动序列
  startBootSequence()
})
</script>

<style scoped lang="scss">
.boot-system {
  height: 100vh;
  background: #000;
  color: #00ff00;
  font-family: 'Courier New', monospace;
  overflow: hidden;

  .boot-screen {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: var(--spacing-xl);
  }

  // BIOS阶段样式
  .bios-section {
    .bios-header {
      text-align: center;
      margin-bottom: var(--spacing-xxl);

      h1 {
        font-size: 24px;
        margin: 0 0 var(--spacing-sm) 0;
        color: #00ff00;
        text-shadow: 0 0 10px #00ff00;
      }

      p {
        font-size: 14px;
        margin: 0 0 var(--spacing-lg) 0;
        color: #00aa00;
      }

      .bios-info {
        .system-info {
          text-align: left;
          max-width: 400px;
          margin: 0 auto;

          p {
            font-size: 12px;
            margin: var(--spacing-xs) 0;
            color: #009900;
          }
        }
      }
    }

    .hardware-check {
      margin-bottom: var(--spacing-xl);

      h3 {
        color: #00ff00;
        margin-bottom: var(--spacing-lg);
        text-align: center;
      }

      .check-items {
        max-width: 600px;
        margin: 0 auto;

        .check-item {
          display: flex;
          font-size: 12px;
          margin: var(--spacing-xs) 0;
          line-height: 1.2;

          &.checking {
            .item-status {
              color: #ffff00;
              animation: blink 1s infinite;
            }
          }

          &.ok {
            .item-status {
              color: #00ff00;
            }
          }

          &.error {
            .item-status {
              color: #ff0000;
            }
          }

          .item-name {
            color: #00aa00;
          }

          .item-dots {
            flex: 1;
            color: #006600;
          }

          .item-status {
            min-width: 60px;
            text-align: right;
          }
        }
      }
    }

    .boot-progress {
      max-width: 600px;
      margin: 0 auto;

      .progress-text {
        text-align: center;
        margin-bottom: var(--spacing-md);
        color: #00ff00;
      }

      .progress-bar {
        height: 20px;
        background: #003300;
        border: 1px solid #00aa00;

        .progress-fill {
          height: 100%;
          background: linear-gradient(90deg, #00ff00, #00aa00);
          transition: width 0.3s ease;
        }
      }
    }
  }

  // 加载阶段样式
  .loading-section {
    text-align: center;
    color: white;
    background: linear-gradient(135deg, #1a1a1a, #2d2d2d);
    padding: var(--spacing-xxl);
    border-radius: var(--border-radius-large);
    max-width: 800px;
    margin: 0 auto;

    .logo-animation {
      margin-bottom: var(--spacing-xxl);

      .rotating-logo {
        width: 80px;
        height: 80px;
        animation: rotate 3s linear infinite;
        margin-bottom: var(--spacing-lg);
      }

      h2 {
        color: #ffffff;
        margin: 0 0 var(--spacing-sm) 0;
        font-family: -apple-system, BlinkMacSystemFont, sans-serif;
      }

      .version {
        color: #999;
        font-size: 14px;
        margin: 0;
      }
    }

    .loading-steps {
      margin-bottom: var(--spacing-xl);
      text-align: left;

      .loading-step {
        display: flex;
        align-items: center;
        padding: var(--spacing-sm) 0;
        font-size: 14px;

        .step-icon {
          width: 20px;
          margin-right: var(--spacing-sm);

          .el-icon {
            font-size: 16px;

            &.loading-icon {
              animation: spin 1s linear infinite;
            }
          }

          .waiting-dot {
            font-size: 16px;
            color: #666;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }

        .step-text {
          flex: 1;
          color: #ccc;
        }

        .step-progress {
          .progress-dots {
            display: flex;
            gap: 2px;

            .dot {
              width: 4px;
              height: 4px;
              border-radius: 50%;
              background: #666;
              animation: dot-pulse 1.5s infinite;

              &:nth-child(2) {
                animation-delay: 0.5s;
              }

              &:nth-child(3) {
                animation-delay: 1s;
              }
            }
          }
        }

        &.completed {
          .step-icon .el-icon {
            color: #00ff00;
          }
          .step-text {
            color: #00aa00;
          }
        }

        &.current {
          .step-text {
            color: #ffffff;
            font-weight: 600;
          }
        }

        &.error {
          .step-icon .el-icon {
            color: #ff4d4f;
          }
          .step-text {
            color: #ff7875;
          }
        }
      }
    }

    .system-status {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: var(--spacing-md);
      font-size: 12px;

      .status-item {
        padding: var(--spacing-sm);
        background: rgba(255, 255, 255, 0.05);
        border-radius: var(--border-radius-base);
        text-align: center;

        strong {
          display: block;
          margin-bottom: var(--spacing-xs);
          color: #ccc;
        }
      }
    }
  }

  // 准备就绪阶段
  .ready-section {
    text-align: center;
    color: white;
    background: linear-gradient(135deg, #1e3a5f, #2d4a6b);
    padding: var(--spacing-xxl);
    border-radius: var(--border-radius-large);
    max-width: 600px;
    margin: 0 auto;

    .ready-animation {
      margin-bottom: var(--spacing-xxl);

      .success-icon {
        font-size: 80px;
        color: #00ff00;
        margin-bottom: var(--spacing-lg);
        animation: pulse 2s infinite;
      }

      h2 {
        color: #ffffff;
        margin: 0 0 var(--spacing-sm) 0;
        font-family: -apple-system, BlinkMacSystemFont, sans-serif;
      }

      p {
        color: #ccc;
        margin: 0;
      }
    }

    .ready-info {
      margin-bottom: var(--spacing-xl);

      .info-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-md);

        .info-item {
          padding: var(--spacing-md);
          background: rgba(255, 255, 255, 0.1);
          border-radius: var(--border-radius-base);

          strong {
            display: block;
            margin-bottom: var(--spacing-xs);
            color: #ccc;
            font-size: 12px;
          }

          span {
            font-size: 18px;
            font-weight: 600;

            &.status-ok {
              color: #00ff00;
            }
          }
        }
      }
    }

    .ready-actions {
      margin-bottom: var(--spacing-lg);

      .el-button {
        padding: var(--spacing-md) var(--spacing-xl);
        font-size: 16px;
      }
    }

    .auto-enter {
      p {
        font-size: 14px;
        color: #999;
        margin: 0;
      }
    }
  }

  // 启动完成阶段
  .boot-complete {
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #1a1a1a, #2d2d2d);
    
    .complete-animation {
      text-align: center;
      color: white;
      
      .success-icon {
        font-size: 100px;
        color: #00ff00;
        margin-bottom: var(--spacing-xl);
        animation: pulse 2s infinite;
      }
      
      h2 {
        color: #ffffff;
        margin: 0 0 var(--spacing-lg) 0;
        font-size: 32px;
        font-family: -apple-system, BlinkMacSystemFont, sans-serif;
      }
      
      p {
        color: #ccc;
        font-size: 18px;
        margin: 0 0 var(--spacing-xl) 0;
      }
      
      .loading-dots {
        display: flex;
        gap: 8px;
        justify-content: center;
        
        .dot {
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background: #00ff00;
          animation: dot-pulse 1.5s infinite;
          
          &:nth-child(2) {
            animation-delay: 0.5s;
          }
          
          &:nth-child(3) {
            animation-delay: 1s;
          }
        }
      }
    }
  }
}

// 动画定义
@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.3; }
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

@keyframes dot-pulse {
  0%, 100% { background: #666; }
  50% { background: #fff; }
}
</style>