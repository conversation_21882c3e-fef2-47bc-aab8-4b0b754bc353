<template>
  <div class="not-found-page">
    <div class="error-content">
      <!-- 404 图标 -->
      <div class="error-icon">
        <el-icon class="icon-404"><Warning /></el-icon>
      </div>

      <!-- 错误信息 -->
      <div class="error-info">
        <h1 class="error-title">404</h1>
        <h3 class="error-subtitle">页面未找到</h3>
        <p class="error-description">
          抱歉，您访问的页面不存在或已被移动。
          <br />
          请检查URL是否正确，或返回首页。
        </p>
      </div>

      <!-- 操作按钮 -->
      <div class="error-actions">
        <el-button type="primary" @click="goHome" size="large">
          <el-icon><HomeFilled /></el-icon>
          返回首页
        </el-button>
        <el-button @click="goBack" size="large">
          <el-icon><Back /></el-icon>
          返回上页
        </el-button>
      </div>

      <!-- 帮助信息 -->
      <div class="help-info">
        <p>常用页面：</p>
        <el-link @click="$router.push('/dashboard')" type="primary">监控台</el-link>
        <el-divider direction="vertical" />
        <el-link @click="$router.push('/monitor')" type="primary">实时监控</el-link>
        <el-divider direction="vertical" />
        <el-link @click="$router.push('/history')" type="primary">历史记录</el-link>
        <el-divider direction="vertical" />
        <el-link @click="$router.push('/settings')" type="primary">系统设置</el-link>
      </div>
    </div>

    <!-- 背景装饰 -->
    <div class="background-decoration">
      <div class="decoration-item decoration-1"></div>
      <div class="decoration-item decoration-2"></div>
      <div class="decoration-item decoration-3"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 404 页面未找到组件
// 作者：仕伟

const router = useRouter()

// 返回首页
const goHome = () => {
  router.push('/dashboard')
}

// 返回上一页
const goBack = () => {
  if (window.history.length > 1) {
    router.go(-1)
  } else {
    goHome()
  }
}
</script>

<style scoped lang="scss">
.not-found-page {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--el-color-primary-light-9), var(--el-color-primary-light-8));
  position: relative;
  overflow: hidden;

  .error-content {
    text-align: center;
    z-index: 1;
    max-width: 600px;
    padding: var(--spacing-xxl);

    .error-icon {
      margin-bottom: var(--spacing-xl);

      .icon-404 {
        font-size: 120px;
        color: var(--el-color-primary);
        opacity: 0.8;
        animation: float 3s ease-in-out infinite;
      }
    }

    .error-info {
      margin-bottom: var(--spacing-xxl);

      .error-title {
        font-size: 80px;
        font-weight: 700;
        color: var(--el-color-primary);
        margin: 0 0 var(--spacing-md) 0;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        font-family: 'Arial', sans-serif;
      }

      .error-subtitle {
        font-size: 32px;
        font-weight: 600;
        color: var(--text-primary);
        margin: 0 0 var(--spacing-lg) 0;
      }

      .error-description {
        font-size: 16px;
        color: var(--text-secondary);
        line-height: 1.6;
        margin: 0 0 var(--spacing-xl) 0;
      }
    }

    .error-actions {
      margin-bottom: var(--spacing-xl);

      .el-button {
        margin: 0 var(--spacing-sm);
        min-width: 120px;

        @media (max-width: $breakpoint-sm) {
          margin: var(--spacing-sm) var(--spacing-xs);
          width: 100%;
          max-width: 200px;
        }
      }
    }

    .help-info {
      padding-top: var(--spacing-lg);
      border-top: 1px solid var(--border-lighter);

      p {
        margin: 0 0 var(--spacing-md) 0;
        color: var(--text-secondary);
        font-size: 14px;
      }

      .el-link {
        margin: 0 var(--spacing-xs);
        font-size: 14px;

        @media (max-width: $breakpoint-sm) {
          display: block;
          margin: var(--spacing-xs) 0;
        }
      }

      .el-divider--vertical {
        @media (max-width: $breakpoint-sm) {
          display: none;
        }
      }
    }
  }

  .background-decoration {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;

    .decoration-item {
      position: absolute;
      border-radius: 50%;
      background: linear-gradient(45deg, 
        rgba(64, 158, 255, 0.1), 
        rgba(64, 158, 255, 0.05)
      );
      animation: float 6s ease-in-out infinite;

      &.decoration-1 {
        width: 200px;
        height: 200px;
        top: 10%;
        left: 10%;
        animation-delay: 0s;
      }

      &.decoration-2 {
        width: 150px;
        height: 150px;
        top: 60%;
        right: 15%;
        animation-delay: 2s;
      }

      &.decoration-3 {
        width: 100px;
        height: 100px;
        bottom: 20%;
        left: 20%;
        animation-delay: 4s;
      }
    }
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

// 移动端适配
@media (max-width: $breakpoint-sm) {
  .not-found-page {
    .error-content {
      padding: var(--spacing-lg);

      .error-icon .icon-404 {
        font-size: 80px;
      }

      .error-info {
        .error-title {
          font-size: 60px;
        }

        .error-subtitle {
          font-size: 24px;
        }

        .error-description {
          font-size: 14px;
        }
      }
    }
  }
}
</style>