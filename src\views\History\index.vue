<template>
  <div class="history-page">
    <div class="page-header">
      <h2>历史记录</h2>
      <p class="page-desc">查询和管理车辆进出记录</p>
    </div>

    <!-- 查询条件 -->
    <el-card class="search-card" shadow="hover">
      <el-form :model="searchForm" :inline="true" label-width="80px">
        <el-form-item label="车牌号">
          <el-input 
            v-model="searchForm.plateNumber" 
            placeholder="请输入车牌号"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 350px"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="请选择" style="width: 120px">
            <el-option label="全部" value="" />
            <el-option label="入场" value="IN" />
            <el-option label="出场" value="OUT" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch" :loading="loading">
            <el-icon><Search /></el-icon>
            查询
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
          <el-button type="success" @click="handleExport">
            <el-icon><Download /></el-icon>
            导出
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card" shadow="hover">
      <el-table 
        :data="tableData" 
        v-loading="loading"
        stripe
        style="width: 100%"
        :default-sort="{ prop: 'timestamp', order: 'descending' }"
      >
        <el-table-column prop="plateNumber" label="车牌号" width="120" align="center">
          <template #default="{ row }">
            <el-tag type="primary" effect="dark">{{ row.plateNumber }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="weight" label="重量(吨)" width="100" align="center">
          <template #default="{ row }">
            <span :class="{ 'weight-overload': row.weight > 40 }">
              {{ row.weight }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="80" align="center">
          <template #default="{ row }">
            <el-tag :type="row.status === 'IN' ? 'success' : 'warning'" size="small">
              {{ row.status === 'IN' ? '入场' : '出场' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="timestamp" label="时间" width="180" align="center">
          <template #default="{ row }">
            {{ formatTime(row.timestamp) }}
          </template>
        </el-table-column>
        <el-table-column label="照片" width="100" align="center">
          <template #default="{ row }">
            <el-button size="small" type="primary" link @click="viewImages(row)">
              <el-icon><Picture /></el-icon>
              查看
            </el-button>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" align="center" fixed="right">
          <template #default="{ row }">
            <el-button size="small" type="primary" link @click="viewDetail(row)">
              详情
            </el-button>
            <el-button size="small" type="danger" link @click="deleteRecord(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 图片预览对话框 -->
    <el-dialog 
      v-model="imageDialogVisible" 
      title="车辆照片" 
      width="60%"
      align-center
    >
      <div class="image-preview">
        <el-image
          v-for="(image, index) in currentImages"
          :key="index"
          :src="image"
          :preview-src-list="currentImages"
          :initial-index="index"
          fit="contain"
          style="width: 200px; height: 150px; margin: 10px;"
          preview-teleported
        />
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
// 历史记录页面
// 作者：仕伟

import { vehicleApi, type VehicleRecord } from '@/services/api'
import dayjs from 'dayjs'

// 搜索表单
const searchForm = reactive({
  plateNumber: '',
  dateRange: [] as string[],
  status: ''
})

// 表格数据
const tableData = ref<VehicleRecord[]>([])
const loading = ref(false)

// 分页
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0
})

// 图片预览
const imageDialogVisible = ref(false)
const currentImages = ref<string[]>([])

// 搜索数据
const handleSearch = async () => {
  loading.value = true
  try {
    const params = {
      plateNumber: searchForm.plateNumber,
      startTime: searchForm.dateRange[0] ? dayjs(searchForm.dateRange[0]).valueOf() : undefined,
      endTime: searchForm.dateRange[1] ? dayjs(searchForm.dateRange[1]).valueOf() : undefined,
      status: searchForm.status as 'IN' | 'OUT' | undefined,
      page: pagination.current,
      limit: pagination.pageSize
    }
    
    const result = await vehicleApi.queryVehicleHistory(params)
    tableData.value = result
    // pagination.total = result.total // TODO: 实际对接时从接口返回总数
    pagination.total = 100 // 模拟数据
  } catch (error) {
    ElMessage.error('查询失败')
  } finally {
    loading.value = false
  }
}

// 重置搜索
const handleReset = () => {
  searchForm.plateNumber = ''
  searchForm.dateRange = []
  searchForm.status = ''
  pagination.current = 1
  handleSearch()
}

// 导出数据
const handleExport = () => {
  ElMessage.info('导出功能开发中...')
  // TODO: 实现数据导出
}

// 查看图片
const viewImages = (row: VehicleRecord) => {
  currentImages.value = row.images
  imageDialogVisible.value = true
}

// 查看详情
const viewDetail = (row: VehicleRecord) => {
  ElMessage.info(`查看 ${row.plateNumber} 详情`)
  // TODO: 实现详情查看
}

// 删除记录
const deleteRecord = (row: VehicleRecord) => {
  ElMessageBox.confirm(`确定要删除车牌号为 ${row.plateNumber} 的记录吗？`, '确认删除', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    ElMessage.success('删除成功')
    handleSearch()
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

// 分页变化
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.current = 1
  handleSearch()
}

const handleCurrentChange = (page: number) => {
  pagination.current = page
  handleSearch()
}

// 格式化时间
const formatTime = (timestamp: number) => {
  return dayjs(timestamp).format('YYYY-MM-DD HH:mm:ss')
}

// 页面加载时获取数据
onMounted(() => {
  handleSearch()
})
</script>

<style scoped lang="scss">
.history-page {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);

  .page-header {
    h2 {
      margin: 0 0 var(--spacing-xs) 0;
      color: var(--text-primary);
      font-size: 24px;
      font-weight: 600;
    }

    .page-desc {
      margin: 0;
      color: var(--text-secondary);
      font-size: 14px;
    }
  }

  .search-card {
    flex-shrink: 0;
  }

  .table-card {
    flex: 1;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
    }

    .el-table {
      flex: 1;
    }

    .pagination-wrapper {
      margin-top: var(--spacing-lg);
      display: flex;
      justify-content: center;
    }
  }

  .weight-overload {
    color: var(--color-weight-overload);
    font-weight: 600;
  }

  .image-preview {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: var(--spacing-sm);
  }
}
</style>