<template>
  <div class="monitor-page">
    <div class="page-header">
      <h2>实时监控</h2>
      <p class="page-desc">多路视频监控和设备状态实时监测</p>
    </div>

    <div class="monitor-content">
      <!-- 视频监控区域 -->
      <div class="video-section">
        <el-row :gutter="16">
          <el-col :span="12">
            <el-card class="video-card" shadow="hover">
              <template #header>
                <div class="card-header">
                  <span>摄像头 1 - 入口监控</span>
                  <el-tag type="success" size="small">在线</el-tag>
                </div>
              </template>
              <div class="video-container">
                <div class="video-placeholder">
                  <el-icon class="video-icon"><VideoCamera /></el-icon>
                  <p>摄像头 1</p>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="12">
            <el-card class="video-card" shadow="hover">
              <template #header>
                <div class="card-header">
                  <span>摄像头 2 - 出口监控</span>
                  <el-tag type="success" size="small">在线</el-tag>
                </div>
              </template>
              <div class="video-container">
                <div class="video-placeholder">
                  <el-icon class="video-icon"><VideoCamera /></el-icon>
                  <p>摄像头 2</p>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 设备状态监控 -->
      <div class="status-section">
        <el-card shadow="hover">
          <template #header>
            <h3>设备状态监控</h3>
          </template>
          <div class="status-grid">
            <div class="status-item">
              <StatusIndicator 
                status="online" 
                label="摄像头系统" 
                icon="VideoCamera" 
              />
            </div>
            <div class="status-item">
              <StatusIndicator 
                status="online" 
                label="称重系统" 
                icon="Scale" 
              />
            </div>
            <div class="status-item">
              <StatusIndicator 
                status="online" 
                label="道闸系统" 
                icon="Operation" 
              />
            </div>
            <div class="status-item">
              <StatusIndicator 
                status="warning" 
                label="网络连接" 
                icon="Connection" 
              />
            </div>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 实时监控页面
// 作者：仕伟

import StatusIndicator from '@/components/StatusIndicator.vue'
</script>

<style scoped lang="scss">
.monitor-page {
  height: 100%;
  display: flex;
  flex-direction: column;

  .page-header {
    margin-bottom: var(--spacing-lg);

    h2 {
      margin: 0 0 var(--spacing-xs) 0;
      color: var(--text-primary);
      font-size: 24px;
      font-weight: 600;
    }

    .page-desc {
      margin: 0;
      color: var(--text-secondary);
      font-size: 14px;
    }
  }

  .monitor-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);

    .video-section {
      flex: 1;

      .video-card {
        height: 400px;

        .card-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
        }

        :deep(.el-card__body) {
          height: calc(100% - 60px);
          padding: 0;
        }

        .video-container {
          height: 100%;
          background: #000;
          display: flex;
          align-items: center;
          justify-content: center;

          .video-placeholder {
            text-align: center;
            color: #666;

            .video-icon {
              font-size: 48px;
              margin-bottom: var(--spacing-md);
            }

            p {
              margin: 0;
              font-size: 16px;
            }
          }
        }
      }
    }

    .status-section {
      .status-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: var(--spacing-md);

        .status-item {
          padding: var(--spacing-md);
          border: 1px solid var(--border-base);
          border-radius: var(--border-radius-base);
          background: var(--bg-card);
        }
      }
    }
  }
}
</style>