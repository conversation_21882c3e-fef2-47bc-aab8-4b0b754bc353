<template>
  <div class="os-boot-system">
    <!-- 系统启动主界面 -->
    <div v-if="!isBootCompleted" class="boot-interface">
      <!-- 顶部状态栏 -->
      <div class="system-status-bar">
        <div class="status-left">
          <div class="system-logo">
            <img src="/logo.svg" alt="Logo" class="logo-icon" />
            <div class="system-title">
              <span class="title-main">河北省采砂监管系统</span>
              <span class="title-version">v{{ systemInfo.version }}</span>
            </div>
          </div>
        </div>
        <div class="status-center">
          <div class="boot-status">
            <span class="status-text">{{ currentBootMessage }}</span>
            <div class="status-dots">
              <span v-for="n in 3" :key="n" class="dot" :class="{ active: n <= activeDot }"></span>
            </div>
          </div>
        </div>
        <div class="status-right">
          <div class="system-time">{{ currentTime }}</div>
          <div class="system-date">{{ currentDate }}</div>
        </div>
      </div>

      <!-- 主启动区域 -->
      <div class="boot-main-area">
        <!-- 左侧：启动进度环 -->
        <div class="progress-section">
          <div class="progress-ring-container">
            <svg class="progress-ring" viewBox="0 0 200 200">
              <!-- 背景圆环 -->
              <circle 
                cx="100" 
                cy="100" 
                r="85" 
                fill="none" 
                stroke="rgba(255,255,255,0.1)" 
                stroke-width="8"
              />
              <!-- 进度圆环 -->
              <circle 
                cx="100" 
                cy="100" 
                r="85" 
                fill="none" 
                stroke="url(#progressGradient)" 
                stroke-width="8" 
                stroke-linecap="round"
                :stroke-dasharray="circumference"
                :stroke-dashoffset="progressOffset"
                class="progress-circle"
              />
              <!-- 渐变定义 -->
              <defs>
                <linearGradient id="progressGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                  <stop offset="0%" style="stop-color:#00D4FF"/>
                  <stop offset="100%" style="stop-color:#1890FF"/>
                </linearGradient>
              </defs>
            </svg>
            
            <!-- 中心内容 -->
            <div class="progress-center">
              <div class="progress-percentage">{{ bootProgress }}%</div>
              <div class="progress-label">系统启动中</div>
              <div class="boot-time">{{ bootTime }}s</div>
            </div>
          </div>

          <!-- 启动阶段指示器 -->
          <div class="phase-indicators">
            <div 
              v-for="(phase, index) in bootPhases"
              :key="phase.id"
              class="phase-item"
              :class="{
                'phase-completed': index < currentPhaseIndex,
                'phase-active': index === currentPhaseIndex,
                'phase-pending': index > currentPhaseIndex
              }"
            >
              <div class="phase-line" v-if="index < bootPhases.length - 1"></div>
              <div class="phase-circle">
                <el-icon v-if="index < currentPhaseIndex" class="phase-check">
                  <Check />
                </el-icon>
                <div v-else-if="index === currentPhaseIndex" class="phase-pulse"></div>
                <span v-else class="phase-number">{{ index + 1 }}</span>
              </div>
              <div class="phase-text">{{ phase.name }}</div>
            </div>
          </div>
        </div>

        <!-- 右侧：系统信息面板 -->
        <div class="info-panel">
          <!-- 系统概览 -->
          <div class="info-card system-overview">
            <div class="card-title">
              <el-icon><Monitor /></el-icon>
              <span>系统概览</span>
            </div>
            <div class="card-body">
              <div class="overview-grid">
                <div class="overview-item">
                  <div class="item-label">设备型号</div>
                  <div class="item-value">{{ systemInfo.deviceModel }}</div>
                </div>
                <div class="overview-item">
                  <div class="item-label">系统版本</div>
                  <div class="item-value">{{ systemInfo.version }}</div>
                </div>
                <div class="overview-item">
                  <div class="item-label">构建版本</div>
                  <div class="item-value">#{{ systemInfo.buildNumber }}</div>
                </div>
                <div class="overview-item">
                  <div class="item-label">启动模式</div>
                  <div class="item-value">自动启动</div>
                </div>
              </div>
            </div>
          </div>

          <!-- 硬件状态 -->
          <div class="info-card hardware-panel">
            <div class="card-title">
              <el-icon><Setting /></el-icon>
              <span>硬件状态</span>
              <div class="status-summary">
                {{ onlineDevicesCount }}/{{ hardwareDevices.length }} 在线
              </div>
            </div>
            <div class="card-body">
              <div class="device-grid">
                <div 
                  v-for="device in hardwareDevices"
                  :key="device.id"
                  class="device-card"
                  :class="{
                    'device-online': device.status === 'online',
                    'device-offline': device.status === 'offline',
                    'device-checking': device.status === 'checking'
                  }"
                >
                  <div class="device-icon">
                    <el-icon>
                      <component :is="device.icon" />
                    </el-icon>
                  </div>
                  <div class="device-name">{{ device.name }}</div>
                  <div class="device-status">
                    <el-icon v-if="device.status === 'online'" class="status-online">
                      <CircleCheckFilled />
                    </el-icon>
                    <el-icon v-else-if="device.status === 'offline'" class="status-offline">
                      <CircleCloseFilled />
                    </el-icon>
                    <div v-else class="status-checking">
                      <div class="checking-dot"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 启动日志 -->
          <div class="info-card boot-logs">
            <div class="card-title">
              <el-icon><Document /></el-icon>
              <span>启动日志</span>
              <div class="log-count">{{ bootLogs.length }} 条</div>
            </div>
            <div class="card-body">
              <div class="log-container" ref="logsContainer">
                <div 
                  v-for="log in bootLogs.slice(-8)"
                  :key="log.id"
                  class="log-line"
                  :class="'log-' + log.status"
                >
                  <span class="log-time">{{ formatLogTime(log.timestamp) }}</span>
                  <span class="log-message">{{ log.message }}</span>
                  <div class="log-status-icon">
                    <el-icon v-if="log.status === 'success'"><Check /></el-icon>
                    <el-icon v-else-if="log.status === 'warning'"><Warning /></el-icon>
                    <div v-else-if="log.status === 'loading'" class="loading-pulse"></div>
                    <el-icon v-else><InfoFilled /></el-icon>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部公司信息 -->
      <div class="company-footer">
        <div class="company-info">
          <span>{{ companyInfo.copyright }}</span>
          <span class="separator">|</span>
          <span>{{ companyInfo.license }}</span>
          <span class="separator">|</span>
          <span>服务热线：{{ companyInfo.phone }}</span>
        </div>
      </div>
    </div>

    <!-- 启动完成界面 -->
    <div v-else class="boot-complete-screen">
      <div class="complete-content">
        <!-- 成功动画 -->
        <div class="success-animation">
          <div class="success-circle">
            <el-icon class="success-icon"><Check /></el-icon>
            <div class="success-ring"></div>
            <div class="success-ring delay-1"></div>
            <div class="success-ring delay-2"></div>
          </div>
        </div>

        <h1 class="complete-title">系统启动完成</h1>
        <p class="complete-subtitle">河北省采砂监管一体机系统已准备就绪</p>

        <!-- 系统状态概览 -->
        <div class="status-overview">
          <div class="status-item">
            <div class="status-number">{{ bootTime }}s</div>
            <div class="status-desc">启动耗时</div>
          </div>
          <div class="status-divider"></div>
          <div class="status-item">
            <div class="status-number">{{ onlineDevicesCount }}</div>
            <div class="status-desc">设备在线</div>
          </div>
          <div class="status-divider"></div>
          <div class="status-item">
            <div class="status-number">{{ systemInfo.version }}</div>
            <div class="status-desc">系统版本</div>
          </div>
        </div>

        <!-- 进入系统 -->
        <div class="enter-system">
          <div class="countdown-info">
            <span class="countdown-text">{{ enterCountdown }}s 后自动进入系统</span>
          </div>
          <el-button 
            type="primary" 
            size="large"
            @click="enterSystem"
            :loading="isEntering"
            class="enter-button"
          >
            <el-icon><Right /></el-icon>
            立即进入系统
          </el-button>
        </div>

        <!-- 公司信息 -->
        <div class="complete-footer">
          <div class="footer-text">{{ companyInfo.name }}</div>
          <div class="footer-details">{{ companyInfo.address }}</div>
        </div>
      </div>
    </div>

    <!-- 背景效果 -->
    <div class="background-effects">
      <div class="bg-grid"></div>
      <div class="bg-particles">
        <div v-for="n in 15" :key="n" class="particle" :style="getParticleStyle(n)"></div>
      </div>
      <div class="bg-glow"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 高级一体机操作系统启动界面
// 作者：仕伟

import { ref, reactive, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
dayjs.locale('zh-cn')
import { 
  CircleCheckFilled, 
  CircleCloseFilled,
  Check,
  Warning,
  InfoFilled,
  Right,
  Monitor,
  Setting,
  Document,
  VideoCamera,
  Platform,
  Operation
} from '@element-plus/icons-vue'

const router = useRouter()

// 系统信息
const systemInfo = reactive({
  version: '2.0.0',
  buildNumber: Date.now().toString().slice(-6),
  deviceModel: 'HB-SM-2024',
})

// 公司信息
const companyInfo = reactive({
  name: '河北省采砂监管系统有限公司',
  address: '河北省石家庄市长安区建华南大街168号',
  phone: '************',
  website: 'www.hebei-sand.gov.cn',
  license: '冀ICP备**********号',
  copyright: '© 2024 河北省采砂监管系统 版权所有'
})

// 时间显示
const currentTime = ref('')
const currentDate = ref('')
const bootStartTime = ref(Date.now())

// 启动状态
const currentPhaseIndex = ref(0)
const bootProgress = ref(0)
const currentBootMessage = ref('正在初始化系统...')
const isBootCompleted = ref(false)
const isEntering = ref(false)
const enterCountdown = ref(5)
const activeDot = ref(1)

// 启动阶段配置
const bootPhases = [
  { id: 1, name: '系统初始化' },
  { id: 2, name: '硬件检测' },
  { id: 3, name: '组件加载' },
  { id: 4, name: '系统就绪' }
]

// 启动日志
const bootLogs = ref([])
const logsContainer = ref(null)

// 硬件设备状态
const hardwareDevices = reactive([
  {
    id: 'camera',
    name: '摄像头',
    icon: 'VideoCamera',
    status: 'checking'
  },
  {
    id: 'scale',
    name: '地磅',
    icon: 'Platform',
    status: 'checking'
  },
  {
    id: 'gate',
    name: '道闸',
    icon: 'Operation',
    status: 'checking'
  },
  {
    id: 'led',
    name: 'LED屏',
    icon: 'Monitor',
    status: 'checking'
  }
])

// 计算属性
const bootTime = computed(() => {
  return ((Date.now() - bootStartTime.value) / 1000).toFixed(1)
})

const onlineDevicesCount = computed(() => {
  return hardwareDevices.filter(device => device.status === 'online').length
})

const circumference = computed(() => {
  return 2 * Math.PI * 85 // r = 85
})

const progressOffset = computed(() => {
  return circumference.value - (bootProgress.value / 100) * circumference.value
})

// 方法
const updateTime = () => {
  const now = dayjs()
  currentTime.value = now.format('HH:mm:ss')
  currentDate.value = now.format('MM月DD日 dddd')
}

const addBootLog = (message: string, level: string = 'info', status: string = 'success') => {
  const log = {
    id: Date.now() + Math.random(),
    timestamp: Date.now(),
    message,
    level,
    status
  }
  bootLogs.value.push(log)
  
  nextTick(() => {
    if (logsContainer.value) {
      logsContainer.value.scrollTop = logsContainer.value.scrollHeight
    }
  })
}

const formatLogTime = (timestamp: number) => {
  return dayjs(timestamp).format('HH:mm:ss')
}

const getParticleStyle = (index: number) => {
  return {
    left: Math.random() * 100 + '%',
    top: Math.random() * 100 + '%',
    animationDelay: Math.random() * 3 + 's',
    animationDuration: (Math.random() * 3 + 2) + 's'
  }
}

// 检测硬件设备状态
const checkHardwareDevice = async (device) => {
  try {
    // 85%成功率
    return Math.random() > 0.15
  } catch (error) {
    return false
  }
}

// 启动序列控制
const runBootSequence = async () => {
  // 启动状态点动画
  const dotAnimation = setInterval(() => {
    activeDot.value = activeDot.value >= 3 ? 1 : activeDot.value + 1
  }, 500)

  // 阶段1：系统初始化
  currentPhaseIndex.value = 0
  currentBootMessage.value = '正在初始化系统内核...'
  addBootLog('河北省采砂监管一体机系统启动', 'info', 'info')
  addBootLog('加载系统内核...', 'info', 'loading')
  
  await new Promise(resolve => setTimeout(resolve, 1200))
  addBootLog('系统内核加载完成', 'info', 'success')
  addBootLog('初始化系统配置...', 'info', 'loading')
  
  await new Promise(resolve => setTimeout(resolve, 800))
  addBootLog('系统配置初始化完成', 'info', 'success')
  bootProgress.value = 25

  // 阶段2：硬件检测
  currentPhaseIndex.value = 1
  currentBootMessage.value = '正在检测硬件设备...'
  addBootLog('开始硬件设备检测', 'info', 'info')
  
  for (let i = 0; i < hardwareDevices.length; i++) {
    const device = hardwareDevices[i]
    addBootLog(`检测${device.name}设备...`, 'info', 'loading')
    
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 800))
    
    const success = await checkHardwareDevice(device)
    device.status = success ? 'online' : 'offline'
    
    if (success) {
      addBootLog(`${device.name}设备检测正常`, 'info', 'success')
    } else {
      addBootLog(`${device.name}设备检测异常`, 'warning', 'warning')
    }
  }
  
  bootProgress.value = 60

  // 阶段3：组件加载
  currentPhaseIndex.value = 2
  currentBootMessage.value = '正在加载系统组件...'
  addBootLog('开始加载系统组件', 'info', 'info')
  
  const components = ['用户界面', '数据处理', '通信模块', '安全组件']
  for (let i = 0; i < components.length; i++) {
    const component = components[i]
    addBootLog(`加载${component}组件...`, 'info', 'loading')
    
    await new Promise(resolve => setTimeout(resolve, 800))
    addBootLog(`${component}组件加载完成`, 'info', 'success')
  }
  
  bootProgress.value = 90

  // 阶段4：系统就绪
  currentPhaseIndex.value = 3
  currentBootMessage.value = '系统准备就绪...'
  addBootLog('执行最终系统检查...', 'info', 'loading')
  
  await new Promise(resolve => setTimeout(resolve, 1500))
  addBootLog('系统检查完成，所有组件运行正常', 'info', 'success')
  addBootLog('河北省采砂监管一体机系统启动成功', 'info', 'success')
  
  bootProgress.value = 100
  clearInterval(dotAnimation)
  
  await new Promise(resolve => setTimeout(resolve, 1000))
  
  isBootCompleted.value = true
  startEnterCountdown()
}

// 开始进入系统倒计时
const startEnterCountdown = () => {
  const timer = setInterval(() => {
    enterCountdown.value--
    if (enterCountdown.value <= 0) {
      clearInterval(timer)
      enterSystem()
    }
  }, 1000)
}

// 进入系统
const enterSystem = () => {
  isEntering.value = true
  setTimeout(() => {
    // 在进入终端前确保全屏模式
    if (!isFullscreen()) {
      enterFullscreen()
    }
    router.push('/terminal')
  }, 800)
}

// 全屏显示功能
const enterFullscreen = () => {
  try {
    const element = document.documentElement
    if (element.requestFullscreen) {
      element.requestFullscreen()
    } else if (element.webkitRequestFullscreen) {
      element.webkitRequestFullscreen()
    } else if (element.mozRequestFullScreen) {
      element.mozRequestFullScreen()
    } else if (element.msRequestFullscreen) {
      element.msRequestFullscreen()
    }
  } catch (error) {
    console.warn('无法进入全屏模式:', error)
  }
}

// 退出全屏功能
const exitFullscreen = () => {
  try {
    if (document.exitFullscreen) {
      document.exitFullscreen()
    } else if (document.webkitExitFullscreen) {
      document.webkitExitFullscreen()
    } else if (document.mozCancelFullScreen) {
      document.mozCancelFullScreen()
    } else if (document.msExitFullscreen) {
      document.msExitFullscreen()
    }
  } catch (error) {
    console.warn('无法退出全屏模式:', error)
  }
}

// 检查是否已经全屏
const isFullscreen = () => {
  return !!(document.fullscreenElement || 
           document.webkitFullscreenElement || 
           document.mozFullScreenElement || 
           document.msFullscreenElement)
}

// 监听全屏状态变化
const handleFullscreenChange = () => {
  // 如果用户退出了全屏，自动重新进入全屏（适用于一体机系统）
  if (!isFullscreen()) {
    setTimeout(() => {
      enterFullscreen()
    }, 1000) // 延迟1秒重新进入，避免频繁切换
  }
}

// 监听键盘事件（可选：阻止某些快捷键）
const handleKeyDown = (event: KeyboardEvent) => {
  // 阻止F11手动切换全屏（如果需要强制保持全屏）
  if (event.key === 'F11') {
    event.preventDefault()
    if (!isFullscreen()) {
      enterFullscreen()
    }
  }
  // 阻止Alt+F4关闭窗口（针对一体机应用）
  if (event.altKey && event.key === 'F4') {
    event.preventDefault()
  }
}

// 生命周期
onMounted(() => {
  updateTime()
  setInterval(updateTime, 1000)
  
  // 添加全屏状态监听器
  document.addEventListener('fullscreenchange', handleFullscreenChange)
  document.addEventListener('webkitfullscreenchange', handleFullscreenChange)
  document.addEventListener('mozfullscreenchange', handleFullscreenChange)
  document.addEventListener('MSFullscreenChange', handleFullscreenChange)
  
  // 添加键盘事件监听器
  document.addEventListener('keydown', handleKeyDown)
  
  // 自动进入全屏模式
  setTimeout(() => {
    if (!isFullscreen()) {
      enterFullscreen()
    }
  }, 500)
  
  setTimeout(() => {
    runBootSequence()
  }, 1000)
})

// 组件销毁时清理事件监听器
onUnmounted(() => {
  document.removeEventListener('fullscreenchange', handleFullscreenChange)
  document.removeEventListener('webkitfullscreenchange', handleFullscreenChange)
  document.removeEventListener('mozfullscreenchange', handleFullscreenChange)
  document.removeEventListener('MSFullscreenChange', handleFullscreenChange)
  document.removeEventListener('keydown', handleKeyDown)
})
</script>

<style scoped lang="scss">
.os-boot-system {
  height: 100vh;
  background: radial-gradient(ellipse at center, #1a1f36 0%, #0f1419 100%);
  color: #ffffff;
  position: relative;
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;

  .boot-interface {
    height: 100vh;
    max-height: 100vh;
    display: flex;
    flex-direction: column;
    position: relative;
    z-index: 10;
    overflow: hidden;
  }

  // 顶部状态栏
  .system-status-bar {
    height: 60px;
    background: rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 30px;

    .status-left {
      .system-logo {
        display: flex;
        align-items: center;
        gap: 12px;

        .logo-icon {
          width: 32px;
          height: 32px;
          filter: drop-shadow(0 0 8px rgba(0, 212, 255, 0.3));
        }

        .system-title {
          display: flex;
          flex-direction: column;
          gap: 2px;

          .title-main {
            font-size: 14px;
            font-weight: 600;
            color: #ffffff;
          }

          .title-version {
            font-size: 11px;
            color: #00D4FF;
            font-family: 'Courier New', monospace;
          }
        }
      }
    }

    .status-center {
      .boot-status {
        display: flex;
        align-items: center;
        gap: 12px;

        .status-text {
          font-size: 14px;
          color: #b0bec5;
        }

        .status-dots {
          display: flex;
          gap: 4px;

          .dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;

            &.active {
              background: #00D4FF;
              box-shadow: 0 0 8px rgba(0, 212, 255, 0.5);
            }
          }
        }
      }
    }

    .status-right {
      text-align: right;

      .system-time {
        font-size: 16px;
        font-weight: 500;
        color: #ffffff;
        font-family: 'Courier New', monospace;
      }

      .system-date {
        font-size: 12px;
        color: #78909c;
        margin-top: 2px;
      }
    }
  }

  // 主启动区域
  .boot-main-area {
    flex: 1;
    display: grid;
    grid-template-columns: 400px 1fr;
    gap: 20px;
    padding: 20px;
    min-height: 0;
    overflow: hidden;

    // 左侧进度区域
    .progress-section {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 20px;
      min-height: 0;

      .progress-ring-container {
        position: relative;
        width: 160px;
        height: 160px;

        .progress-ring {
          width: 100%;
          height: 100%;
          transform: rotate(-90deg);
        }

        .progress-circle {
          transition: stroke-dashoffset 0.5s ease;
        }

        .progress-center {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          text-align: center;

          .progress-percentage {
            font-size: 24px;
            font-weight: 700;
            color: #00D4FF;
            font-family: 'Courier New', monospace;
            margin-bottom: 4px;
          }

          .progress-label {
            font-size: 14px;
            color: #b0bec5;
            margin-bottom: 8px;
          }

          .boot-time {
            font-size: 12px;
            color: #78909c;
            font-family: 'Courier New', monospace;
          }
        }
      }

      .phase-indicators {
        display: flex;
        flex-direction: column;
        gap: 16px;
        width: 100%;
        flex: 1;
        min-height: 0;

        .phase-item {
          display: flex;
          align-items: center;
          gap: 16px;
          position: relative;

          .phase-line {
            position: absolute;
            left: 15px;
            top: 32px;
            width: 2px;
            height: 16px;
            background: rgba(255, 255, 255, 0.1);
          }

          .phase-circle {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            z-index: 1;
            transition: all 0.3s ease;

            .phase-check {
              color: #00D4FF;
              font-size: 16px;
            }

            .phase-pulse {
              width: 16px;
              height: 16px;
              border-radius: 50%;
              background: #00D4FF;
              animation: pulse 2s infinite;
            }

            .phase-number {
              font-size: 12px;
              color: #78909c;
              font-weight: 600;
            }
          }

          .phase-text {
            font-size: 14px;
            color: #b0bec5;
            font-weight: 500;
          }

          &.phase-completed {
            .phase-circle {
              background: rgba(0, 212, 255, 0.2);
              border: 2px solid #00D4FF;
            }

            .phase-text {
              color: #ffffff;
            }

            .phase-line {
              background: #00D4FF;
            }
          }

          &.phase-active {
            .phase-circle {
              background: rgba(0, 212, 255, 0.3);
              border: 2px solid #00D4FF;
              box-shadow: 0 0 16px rgba(0, 212, 255, 0.3);
            }

            .phase-text {
              color: #00D4FF;
              font-weight: 600;
            }
          }
        }
      }
    }

    // 右侧信息面板
    .info-panel {
      display: flex;
      flex-direction: column;
      gap: 16px;
      min-height: 0;
      overflow: hidden;

      .info-card {
        background: rgba(255, 255, 255, 0.05);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 16px;
        overflow: hidden;

        .card-title {
          height: 40px;
          background: rgba(0, 0, 0, 0.2);
          display: flex;
          align-items: center;
          gap: 12px;
          padding: 0 16px;
          font-size: 13px;
          font-weight: 600;
          color: #ffffff;
          border-bottom: 1px solid rgba(255, 255, 255, 0.1);

          .el-icon {
            color: #00D4FF;
          }

          .status-summary, .log-count {
            margin-left: auto;
            font-size: 12px;
            color: #00D4FF;
            font-family: 'Courier New', monospace;
          }
        }

        .card-body {
          padding: 16px;
        }

        &.system-overview {
          .overview-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;

            .overview-item {
              .item-label {
                font-size: 12px;
                color: #78909c;
                margin-bottom: 4px;
              }

              .item-value {
                font-size: 14px;
                color: #ffffff;
                font-weight: 500;
                font-family: 'Courier New', monospace;
              }
            }
          }
        }

        &.hardware-panel {
          .device-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 16px;

            .device-card {
              background: rgba(255, 255, 255, 0.05);
              border: 1px solid rgba(255, 255, 255, 0.1);
              border-radius: 12px;
              padding: 16px;
              display: flex;
              flex-direction: column;
              align-items: center;
              gap: 8px;
              transition: all 0.3s ease;
              position: relative;

              .device-icon {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                background: rgba(255, 255, 255, 0.1);
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 18px;
                color: #b0bec5;
              }

              .device-name {
                font-size: 13px;
                color: #b0bec5;
                font-weight: 500;
              }

              .device-status {
                position: absolute;
                top: 12px;
                right: 12px;

                .status-online {
                  color: #00e676;
                  font-size: 14px;
                }

                .status-offline {
                  color: #ff5722;
                  font-size: 14px;
                }

                .status-checking {
                  .checking-dot {
                    width: 8px;
                    height: 8px;
                    border-radius: 50%;
                    background: #00D4FF;
                    animation: pulse 1.5s infinite;
                  }
                }
              }

              &.device-online {
                border-color: rgba(0, 230, 118, 0.3);
                background: rgba(0, 230, 118, 0.05);

                .device-icon {
                  color: #00e676;
                  background: rgba(0, 230, 118, 0.1);
                }

                .device-name {
                  color: #ffffff;
                }
              }

              &.device-offline {
                border-color: rgba(255, 87, 34, 0.3);
                background: rgba(255, 87, 34, 0.05);

                .device-icon {
                  color: #ff5722;
                  background: rgba(255, 87, 34, 0.1);
                }
              }

              &.device-checking {
                border-color: rgba(0, 212, 255, 0.3);
                background: rgba(0, 212, 255, 0.05);

                .device-icon {
                  color: #00D4FF;
                  background: rgba(0, 212, 255, 0.1);
                  animation: pulse 2s infinite;
                }
              }
            }
          }
        }

        &.boot-logs {
          flex: 1;
          min-height: 0;

          .log-container {
            max-height: 160px;
            overflow-y: auto;
            
            &::-webkit-scrollbar {
              width: 4px;
            }

            &::-webkit-scrollbar-thumb {
              background: rgba(0, 212, 255, 0.3);
              border-radius: 2px;
            }

            .log-line {
              display: flex;
              align-items: center;
              gap: 12px;
              padding: 8px 0;
              border-bottom: 1px solid rgba(255, 255, 255, 0.05);
              font-size: 12px;

              .log-time {
                color: #78909c;
                font-family: 'Courier New', monospace;
                min-width: 60px;
              }

              .log-message {
                flex: 1;
                color: #b0bec5;
              }

              .log-status-icon {
                .el-icon {
                  font-size: 12px;
                }

                .loading-pulse {
                  width: 6px;
                  height: 6px;
                  border-radius: 50%;
                  background: #00D4FF;
                  animation: pulse 1s infinite;
                }
              }

              &.log-success {
                .log-status-icon .el-icon {
                  color: #00e676;
                }
              }

              &.log-warning {
                .log-message {
                  color: #ffc107;
                }

                .log-status-icon .el-icon {
                  color: #ffc107;
                }
              }
            }
          }
        }
      }
    }
  }

  // 底部公司信息
  .company-footer {
    height: 32px;
    background: rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(20px);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;

    .company-info {
      font-size: 10px;
      color: #78909c;

      .separator {
        margin: 0 8px;
        color: rgba(255, 255, 255, 0.2);
      }
    }
  }

  // 启动完成界面
  .boot-complete-screen {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    z-index: 10;

    .complete-content {
      text-align: center;
      max-width: 600px;

      .success-animation {
        margin-bottom: 40px;
        position: relative;

        .success-circle {
          width: 120px;
          height: 120px;
          border-radius: 50%;
          background: linear-gradient(135deg, #00D4FF, #0066cc);
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 0 auto;
          position: relative;

          .success-icon {
            font-size: 60px;
            color: white;
            animation: scaleIn 0.6s ease-out;
          }

          .success-ring {
            position: absolute;
            border: 2px solid rgba(0, 212, 255, 0.3);
            border-radius: 50%;
            animation: ringExpand 2s ease-out infinite;

            &.delay-1 {
              animation-delay: 0.5s;
            }

            &.delay-2 {
              animation-delay: 1s;
            }
          }
        }
      }

      .complete-title {
        font-size: 36px;
        font-weight: 700;
        color: #ffffff;
        margin: 0 0 16px 0;
        text-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
      }

      .complete-subtitle {
        font-size: 16px;
        color: #b0bec5;
        margin: 0 0 40px 0;
      }

      .status-overview {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 40px;
        margin-bottom: 40px;

        .status-item {
          text-align: center;

          .status-number {
            font-size: 24px;
            font-weight: 700;
            color: #00D4FF;
            font-family: 'Courier New', monospace;
            margin-bottom: 4px;
          }

          .status-desc {
            font-size: 12px;
            color: #78909c;
          }
        }

        .status-divider {
          width: 1px;
          height: 40px;
          background: rgba(255, 255, 255, 0.1);
        }
      }

      .enter-system {
        margin-bottom: 40px;

        .countdown-info {
          margin-bottom: 20px;

          .countdown-text {
            font-size: 14px;
            color: #b0bec5;
          }
        }

        .enter-button {
          height: 48px;
          padding: 0 32px;
          font-size: 16px;
          font-weight: 600;
          border-radius: 24px;
          background: linear-gradient(135deg, #00D4FF, #0066cc);
          border: none;
          box-shadow: 0 8px 20px rgba(0, 212, 255, 0.3);
          transition: all 0.3s ease;

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 24px rgba(0, 212, 255, 0.4);
          }
        }
      }

      .complete-footer {
        .footer-text {
          font-size: 14px;
          color: #b0bec5;
          margin-bottom: 4px;
        }

        .footer-details {
          font-size: 12px;
          color: #78909c;
        }
      }
    }
  }

  // 背景效果
  .background-effects {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;

    .bg-grid {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-image: 
        linear-gradient(rgba(0, 212, 255, 0.02) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0, 212, 255, 0.02) 1px, transparent 1px);
      background-size: 50px 50px;
      animation: gridMove 30s linear infinite;
    }

    .bg-particles {
      .particle {
        position: absolute;
        width: 2px;
        height: 2px;
        background: rgba(0, 212, 255, 0.3);
        border-radius: 50%;
        animation: float 3s ease-in-out infinite;
      }
    }

    .bg-glow {
      position: absolute;
      top: 50%;
      left: 50%;
      width: 800px;
      height: 800px;
      transform: translate(-50%, -50%);
      background: radial-gradient(circle, rgba(0, 212, 255, 0.1) 0%, transparent 70%);
      animation: glow 4s ease-in-out infinite alternate;
    }
  }
}

// 动画定义
@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

@keyframes scaleIn {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes ringExpand {
  0% {
    width: 120px;
    height: 120px;
    opacity: 1;
  }
  100% {
    width: 200px;
    height: 200px;
    opacity: 0;
  }
}

@keyframes gridMove {
  0% { transform: translate(0, 0); }
  100% { transform: translate(50px, 50px); }
}

@keyframes float {
  0%, 100% { transform: translateY(0px) scale(1); }
  50% { transform: translateY(-10px) scale(1.1); }
}

@keyframes glow {
  0% { opacity: 0.3; transform: translate(-50%, -50%) scale(1); }
  100% { opacity: 0.6; transform: translate(-50%, -50%) scale(1.1); }
}

// 响应式适配
@media (max-height: 900px) {
  .os-boot-system {
    .boot-main-area {
      padding: 16px;
      gap: 16px;

      .progress-section {
        gap: 16px;

        .progress-ring-container {
          width: 140px;
          height: 140px;

          .progress-center .progress-percentage {
            font-size: 20px;
          }
        }

        .phase-indicators {
          gap: 12px;

          .phase-item {
            .phase-text {
              font-size: 13px;
            }
          }
        }
      }

      .info-panel {
        gap: 12px;

        .info-card {
          .card-title {
            height: 36px;
            padding: 0 12px;
            font-size: 12px;
          }

          .card-body {
            padding: 12px;
          }

          &.boot-logs .log-container {
            max-height: 120px;
          }
        }
      }
    }

    .company-footer {
      height: 28px;
      
      .company-info {
        font-size: 9px;
      }
    }
  }
}

@media (max-width: 1200px) {
  .os-boot-system {
    .boot-main-area {
      grid-template-columns: 1fr;
      gap: 20px;
      padding: 20px;

      .progress-section {
        flex-direction: row;
        justify-content: space-between;
        align-items: flex-start;

        .phase-indicators {
          flex-direction: row;
          gap: 30px;
          width: auto;
          flex: none;

          .phase-item {
            flex-direction: column;
            text-align: center;
            gap: 8px;

            .phase-line {
              display: none;
            }
          }
        }
      }

      .info-panel {
        .info-card.hardware-panel .device-grid {
          grid-template-columns: repeat(4, 1fr);
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .os-boot-system {
    .system-status-bar {
      padding: 0 16px;
      height: 50px;

      .status-center {
        display: none;
      }

      .system-logo .logo-icon {
        width: 24px;
        height: 24px;
      }

      .system-title .title-main {
        font-size: 12px;
      }
    }

    .boot-main-area {
      padding: 16px;
      gap: 16px;

      .progress-section {
        .progress-ring-container {
          width: 120px;
          height: 120px;

          .progress-center .progress-percentage {
            font-size: 18px;
          }
        }

        .phase-indicators {
          gap: 16px;

          .phase-item {
            .phase-text {
              font-size: 11px;
            }

            .phase-circle {
              width: 24px;
              height: 24px;
            }
          }
        }
      }

      .info-panel {
        .info-card.hardware-panel .device-grid {
          grid-template-columns: repeat(2, 1fr);
          gap: 10px;
        }

        .info-card .card-body {
          padding: 12px;
        }
      }
    }

    .company-footer {
      height: 24px;
      padding: 0 16px;

      .company-info {
        font-size: 8px;
      }
    }

    .boot-complete-screen {
      .complete-content {
        padding: 16px;

        .complete-title {
          font-size: 24px;
        }

        .status-overview {
          gap: 16px;
        }
      }
    }
  }
}

// 超小屏幕适配
@media (max-height: 700px) {
  .os-boot-system {
    .system-status-bar {
      height: 40px;
    }

    .boot-main-area {
      padding: 12px;
      gap: 12px;

      .progress-section {
        .progress-ring-container {
          width: 100px;
          height: 100px;

          .progress-center {
            .progress-percentage {
              font-size: 16px;
            }

            .progress-label, .boot-time {
              font-size: 11px;
            }
          }
        }

        .phase-indicators {
          gap: 8px;

          .phase-item {
            .phase-text {
              font-size: 10px;
            }

            .phase-circle {
              width: 20px;
              height: 20px;
            }
          }
        }
      }

      .info-panel {
        gap: 8px;

        .info-card {
          .card-title {
            height: 28px;
            padding: 0 8px;
            font-size: 11px;
          }

          .card-body {
            padding: 8px;
          }

          &.boot-logs .log-container {
            max-height: 80px;
          }

          &.system-overview .overview-grid {
            gap: 8px;
          }

          &.hardware-panel .device-grid {
            gap: 8px;

            .device-card {
              padding: 8px;
            }
          }
        }
      }
    }

    .company-footer {
      height: 20px;

      .company-info {
        font-size: 7px;
      }
    }
  }
}
</style>