<template>
  <el-card class="weight-display" shadow="hover">
    <template #header>
      <div class="card-header">
        <div class="header-left">
          <el-icon class="header-icon"><Scale /></el-icon>
          <span class="header-title">实时称重</span>
        </div>
        <div class="header-right">
          <el-button size="small" @click="handleTare" :loading="tareLoading">
            调零
          </el-button>
          <el-button size="small" type="primary" @click="captureWeight">
            确认重量
          </el-button>
        </div>
      </div>
    </template>

    <div class="weight-content">
      <!-- 主要重量显示 -->
      <div class="weight-main">
        <div class="weight-value" :class="weightStatusClass">
          <span class="number">{{ formatWeight(currentWeight) }}</span>
          <span class="unit">吨</span>
        </div>
        <div class="weight-status">
          <el-tag :type="statusTagType" size="small">
            {{ weightStatusText }}
          </el-tag>
        </div>
      </div>

      <!-- 重量趋势图 -->
      <div class="weight-chart">
        <div class="chart-header">
          <span class="chart-title">重量趋势</span>
          <span class="chart-time">最近10分钟</span>
        </div>
        <div class="chart-container" ref="chartContainer"></div>
      </div>

      <!-- 重量统计信息 -->
      <div class="weight-stats">
        <div class="stat-item">
          <div class="stat-label">最大值</div>
          <div class="stat-value">{{ maxWeight }} 吨</div>
        </div>
        <div class="stat-item">
          <div class="stat-label">最小值</div>
          <div class="stat-value">{{ minWeight }} 吨</div>
        </div>
        <div class="stat-item">
          <div class="stat-label">稳定性</div>
          <div class="stat-value">{{ stabilityText }}</div>
        </div>
      </div>
    </div>
  </el-card>
</template>

<script setup lang="ts">
// 称重显示组件
// 作者：仕伟

import { weightApi } from '@/services/api'
import { useWebSocket } from '@/services/websocket'
import * as echarts from 'echarts'
import type { ECharts } from 'echarts'

// 组件状态
const currentWeight = ref(0)
const isStable = ref(true)
const tareLoading = ref(false)
const maxWeight = ref(0)
const minWeight = ref(0)
const weightHistory = ref<Array<{ time: string, value: number }>>([])

// DOM引用
const chartContainer = ref<HTMLElement>()
let chart: ECharts | null = null

// WebSocket连接
const { subscribe } = useWebSocket()

// 重量状态计算
const weightStatusClass = computed(() => {
  if (currentWeight.value > 40) return 'weight-overload'
  if (currentWeight.value < 5) return 'weight-light'
  return 'weight-normal'
})

const weightStatusText = computed(() => {
  if (!isStable.value) return '不稳定'
  if (currentWeight.value > 40) return '超重警告'
  if (currentWeight.value < 5) return '空载'
  return '正常'
})

const statusTagType = computed(() => {
  if (!isStable.value) return 'warning'
  if (currentWeight.value > 40) return 'danger'
  if (currentWeight.value < 5) return 'info'
  return 'success'
})

const stabilityText = computed(() => {
  return isStable.value ? '稳定' : '波动中'
})

// 格式化重量显示
const formatWeight = (weight: number) => {
  return weight.toFixed(1)
}

// 调零操作
const handleTare = async () => {
  tareLoading.value = true
  try {
    await weightApi.tareWeight()
    ElMessage.success('调零成功')
    currentWeight.value = 0
  } catch (error) {
    ElMessage.error('调零失败')
  } finally {
    tareLoading.value = false
  }
}

// 确认重量
const captureWeight = () => {
  if (!isStable.value) {
    ElMessage.warning('重量不稳定，请稍等再确认')
    return
  }
  
  ElMessage.success(`已确认重量：${formatWeight(currentWeight.value)}吨`)
  
  // TODO: 这里可以触发确认重量的业务逻辑
  console.log('确认重量:', currentWeight.value)
}

// 初始化图表
const initChart = () => {
  if (!chartContainer.value) return
  
  chart = echarts.init(chartContainer.value)
  
  const option = {
    tooltip: {
      trigger: 'axis',
      formatter: '{b}<br/>重量: {c} 吨'
    },
    xAxis: {
      type: 'category',
      data: weightHistory.value.map(item => item.time),
      boundaryGap: false,
      axisLabel: {
        fontSize: 10,
        color: 'var(--text-secondary)'
      },
      axisLine: {
        lineStyle: {
          color: 'var(--border-base)'
        }
      }
    },
    yAxis: {
      type: 'value',
      name: '重量(吨)',
      axisLabel: {
        fontSize: 10,
        color: 'var(--text-secondary)'
      },
      axisLine: {
        lineStyle: {
          color: 'var(--border-base)'
        }
      },
      splitLine: {
        lineStyle: {
          color: 'var(--border-lighter)'
        }
      }
    },
    series: [{
      data: weightHistory.value.map(item => item.value),
      type: 'line',
      smooth: true,
      lineStyle: {
        color: '#409EFF',
        width: 2
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            { offset: 0, color: 'rgba(64, 158, 255, 0.3)' },
            { offset: 1, color: 'rgba(64, 158, 255, 0.1)' }
          ]
        }
      },
      symbol: 'none'
    }],
    grid: {
      left: 40,
      right: 20,
      top: 20,
      bottom: 30
    }
  }
  
  chart.setOption(option)
}

// 更新图表数据
const updateChart = () => {
  if (!chart) return
  
  chart.setOption({
    xAxis: {
      data: weightHistory.value.map(item => item.time)
    },
    series: [{
      data: weightHistory.value.map(item => item.value)
    }]
  })
}

// 添加重量数据点
const addWeightPoint = (weight: number, stable: boolean) => {
  const now = new Date()
  const timeStr = now.toLocaleTimeString('zh-CN', { 
    hour12: false, 
    hour: '2-digit', 
    minute: '2-digit',
    second: '2-digit'
  })
  
  // 添加新数据点
  weightHistory.value.push({
    time: timeStr,
    value: weight
  })
  
  // 保持最近20个数据点
  if (weightHistory.value.length > 20) {
    weightHistory.value.shift()
  }
  
  // 更新统计信息
  const values = weightHistory.value.map(item => item.value)
  maxWeight.value = Math.max(...values, weight)
  minWeight.value = Math.min(...values, weight)
  
  // 更新图表
  updateChart()
}

// 定时获取重量数据
const startWeightMonitoring = () => {
  const timer = setInterval(async () => {
    try {
      const weightData = await weightApi.getCurrentWeight()
      currentWeight.value = weightData.value
      isStable.value = weightData.stable
      
      // 添加到历史数据
      addWeightPoint(weightData.value, weightData.stable)
    } catch (error) {
      console.error('获取重量数据失败:', error)
    }
  }, 2000) // 每2秒获取一次
  
  // 组件卸载时清理定时器
  onUnmounted(() => {
    clearInterval(timer)
  })
}

// 监听WebSocket重量数据推送
onMounted(() => {
  // 初始化图表
  nextTick(() => {
    initChart()
  })
  
  // 开始监控重量数据
  startWeightMonitoring()
  
  // 订阅WebSocket重量数据
  subscribe('WEIGHT_DATA', (data) => {
    currentWeight.value = data.value
    isStable.value = data.stable
    addWeightPoint(data.value, data.stable)
  })
  
  // 窗口大小变化时重新调整图表
  const handleResize = () => {
    if (chart) {
      chart.resize()
    }
  }
  
  window.addEventListener('resize', handleResize)
  
  onUnmounted(() => {
    window.removeEventListener('resize', handleResize)
    if (chart) {
      chart.dispose()
    }
  })
})
</script>

<style scoped lang="scss">
.weight-display {
  height: 100%;
  
  :deep(.el-card__body) {
    height: calc(100% - 60px);
    padding: var(--spacing-md);
  }

  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;

    .header-left {
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);

      .header-icon {
        color: var(--el-color-primary);
        font-size: 18px;
      }

      .header-title {
        font-size: 16px;
        font-weight: 600;
        color: var(--text-primary);
      }
    }
  }

  .weight-content {
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);

    .weight-main {
      text-align: center;
      padding: var(--spacing-lg) 0;
      border-bottom: 1px solid var(--border-base);

      .weight-value {
        margin-bottom: var(--spacing-sm);
        transition: var(--transition-base);

        .number {
          font-size: 48px;
          font-weight: 700;
          font-family: 'Courier New', monospace;
        }

        .unit {
          font-size: 18px;
          font-weight: 500;
          margin-left: var(--spacing-sm);
          color: var(--text-secondary);
        }

        &.weight-normal {
          .number { color: var(--color-weight-normal); }
        }

        &.weight-overload {
          .number { 
            color: var(--color-weight-overload);
            animation: pulse 1.5s ease-in-out infinite;
          }
        }

        &.weight-light {
          .number { color: var(--text-secondary); }
        }
      }
    }

    .weight-chart {
      flex: 1;
      min-height: 200px;

      .chart-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: var(--spacing-sm);

        .chart-title {
          font-size: 14px;
          font-weight: 500;
          color: var(--text-primary);
        }

        .chart-time {
          font-size: 12px;
          color: var(--text-secondary);
        }
      }

      .chart-container {
        height: 180px;
        width: 100%;
      }
    }

    .weight-stats {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: var(--spacing-md);
      padding: var(--spacing-md) 0;
      border-top: 1px solid var(--border-base);

      .stat-item {
        text-align: center;

        .stat-label {
          font-size: 12px;
          color: var(--text-secondary);
          margin-bottom: var(--spacing-xs);
        }

        .stat-value {
          font-size: 14px;
          font-weight: 600;
          color: var(--text-primary);
        }
      }
    }
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}
</style>