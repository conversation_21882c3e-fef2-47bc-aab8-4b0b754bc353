import axios from 'axios'
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import { ElMessage } from 'element-plus'

// API服务层 - 预留后端接口对接位置
// 作者：仕伟

// 创建 axios 实例
const api: AxiosInstance = axios.create({
  baseURL: '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  (config: AxiosRequestConfig) => {
    // TODO: 添加认证 token
    // const token = getToken()
    // if (token) {
    //   config.headers!.Authorization = `Bearer ${token}`
    // }
    
    console.log('API请求:', config.method?.toUpperCase(), config.url, config.data)
    return config
  },
  (error) => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response: AxiosResponse) => {
    const { data } = response
    
    // 统一处理响应格式
    if (data.code === 0) {
      return data.data
    } else {
      ElMessage.error(data.message || '请求失败')
      return Promise.reject(new Error(data.message || '请求失败'))
    }
  },
  (error) => {
    console.error('响应错误:', error)
    
    if (error.response) {
      const status = error.response.status
      switch (status) {
        case 401:
          ElMessage.error('未授权，请重新登录')
          // TODO: 跳转到登录页
          break
        case 403:
          ElMessage.error('拒绝访问')
          break
        case 404:
          ElMessage.error('请求的资源不存在')
          break
        case 500:
          ElMessage.error('服务器内部错误')
          break
        default:
          ElMessage.error(`请求失败: ${status}`)
      }
    } else if (error.request) {
      ElMessage.error('网络连接失败，请检查网络')
    } else {
      ElMessage.error('请求配置错误')
    }
    
    return Promise.reject(error)
  }
)

// 车辆相关接口
export const vehicleApi = {
  // 获取项目配置信息
  getProjectConfig: (): Promise<ProjectConfig> => {
    // TODO: 实际对接时替换为真实接口
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          projectId: 'HB-CS-001',
          projectName: '河北省采砂监管项目',
          stationName: '采砂站点1号',
          equipmentId: 'EQ-001',
          version: '2.0.0'
        })
      }, 1000)
    })
    // return api.get('/car/getProjectId')
  },

  // 提交车辆数据
  submitVehicleData: (data: VehicleSubmitData): Promise<any> => {
    // TODO: 实际对接时替换为真实接口
    console.log('提交车辆数据:', data)
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({ success: true, id: Date.now() })
      }, 500)
    })
    // return api.post('/car/submitData', data)
  },

  // 查询车辆历史记录
  queryVehicleHistory: (params: VehicleQueryParams): Promise<VehicleRecord[]> => {
    // TODO: 实际对接时替换为真实接口
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve([
          {
            id: '1',
            plateNumber: '冀A12345',
            weight: 35.5,
            timestamp: Date.now() - 3600000,
            status: 'OUT',
            images: ['/mock-image1.jpg', '/mock-image2.jpg']
          },
          {
            id: '2', 
            plateNumber: '冀B67890',
            weight: 28.2,
            timestamp: Date.now() - 7200000,
            status: 'IN',
            images: ['/mock-image3.jpg', '/mock-image4.jpg']
          }
        ])
      }, 800)
    })
    // return api.get('/car/getDataByCarNumber', { params })
  },

  // 验证车牌号权限
  validatePlateNumber: (plateNumber: string): Promise<PlateValidationResult> => {
    // TODO: 实际对接时替换为真实接口
    return new Promise((resolve) => {
      setTimeout(() => {
        const isValid = Math.random() > 0.3 // 70% 概率有效
        resolve({
          valid: isValid,
          plateNumber,
          hasPermit: isValid,
          permitInfo: isValid ? {
            permitId: 'P-001',
            validUntil: Date.now() + 86400000,
            allowedWeight: 40
          } : undefined
        })
      }, 600)
    })
    // return api.get(`/car/getCarNumById?plateNumber=${plateNumber}`)
  }
}

// 称重相关接口
export const weightApi = {
  // 获取当前重量
  getCurrentWeight: (): Promise<WeightData> => {
    // TODO: 实际对接时替换为真实接口
    return new Promise((resolve) => {
      const weight = 20 + Math.random() * 30 // 模拟20-50吨
      resolve({
        value: Number(weight.toFixed(1)),
        unit: '吨',
        stable: Math.random() > 0.2,
        timestamp: Date.now()
      })
    })
    // return api.get('/weight/getWeight')
  },

  // 重量调零
  tareWeight: (): Promise<any> => {
    // TODO: 实际对接时替换为真实接口
    console.log('执行重量调零')
    return new Promise((resolve) => {
      setTimeout(() => resolve({ success: true }), 500)
    })
    // return api.post('/weight/tare')
  },

  // 获取重量历史数据
  getWeightHistory: (params: TimeRangeParams): Promise<WeightHistoryRecord[]> => {
    // TODO: 实际对接时替换为真实接口
    return new Promise((resolve) => {
      const records: WeightHistoryRecord[] = []
      const now = Date.now()
      
      for (let i = 0; i < 20; i++) {
        records.push({
          timestamp: now - i * 60000,
          weight: 20 + Math.random() * 20,
          stable: true
        })
      }
      
      resolve(records)
    })
    // return api.get('/weight/history', { params })
  }
}

// 设备控制接口
export const deviceApi = {
  // 获取设备状态
  getDeviceStatus: (): Promise<DeviceStatusResponse> => {
    // TODO: 实际对接时替换为真实接口
    return new Promise((resolve) => {
      resolve({
        camera: Math.random() > 0.1 ? 'online' : 'offline',
        scale: Math.random() > 0.05 ? 'online' : 'offline', 
        gate: Math.random() > 0.05 ? 'online' : 'offline'
      })
    })
    // return api.get('/device/status')
  },

  // 触发抬杆
  triggerGate: (): Promise<any> => {
    // TODO: 实际对接时替换为真实接口
    console.log('触发道闸抬杆')
    return new Promise((resolve) => {
      setTimeout(() => resolve({ success: true, message: '抬杆成功' }), 1000)
    })
    // return api.post('/device/gate/trigger')
  },

  // 手动抓拍
  capturePhoto: (): Promise<CaptureResult> => {
    // TODO: 实际对接时替换为真实接口
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          imageUrl: `/mock-capture-${Date.now()}.jpg`,
          timestamp: Date.now()
        })
      }, 800)
    })
    // return api.post('/device/camera/capture')
  }
}

// 系统配置接口
export const configApi = {
  // 获取系统配置
  getSystemConfig: (): Promise<SystemConfig> => {
    // TODO: 实际对接时替换为真实接口
    return new Promise((resolve) => {
      resolve({
        mqttConfig: {
          host: '127.0.0.1',
          port: 8083,
          username: 'admin',
          password: 'admin123'
        },
        videoConfig: {
          primaryStream: 'rtmp://127.0.0.1/live/camera1',
          secondaryStream: 'rtmp://127.0.0.1/live/camera2'
        },
        weightConfig: {
          serialPort: 'COM3',
          baudRate: 9600,
          threshold: 30
        }
      })
    })
    // return api.get('/config')
  },

  // 更新系统配置
  updateSystemConfig: (config: SystemConfig): Promise<any> => {
    // TODO: 实际对接时替换为真实接口
    console.log('更新系统配置:', config)
    return new Promise((resolve) => {
      setTimeout(() => resolve({ success: true }), 500)
    })
    // return api.put('/config', config)
  }
}

// 导出默认实例
export default api

// TypeScript 类型定义
export interface ProjectConfig {
  projectId: string
  projectName: string
  stationName: string
  equipmentId: string
  version: string
}

export interface VehicleSubmitData {
  plateNumber: string
  weight: number
  status: 'IN' | 'OUT'
  timestamp: number
  images?: string[]
}

export interface VehicleQueryParams {
  plateNumber?: string
  startTime?: number
  endTime?: number
  status?: 'IN' | 'OUT'
  page?: number
  limit?: number
}

export interface VehicleRecord {
  id: string
  plateNumber: string
  weight: number
  timestamp: number
  status: 'IN' | 'OUT'
  images: string[]
}

export interface PlateValidationResult {
  valid: boolean
  plateNumber: string
  hasPermit: boolean
  permitInfo?: {
    permitId: string
    validUntil: number
    allowedWeight: number
  }
}

export interface WeightData {
  value: number
  unit: string
  stable: boolean
  timestamp: number
}

export interface TimeRangeParams {
  startTime: number
  endTime: number
}

export interface WeightHistoryRecord {
  timestamp: number
  weight: number
  stable: boolean
}

export interface DeviceStatusResponse {
  camera: 'online' | 'offline' | 'warning'
  scale: 'online' | 'offline' | 'warning'
  gate: 'online' | 'offline' | 'warning'
}

export interface CaptureResult {
  success: boolean
  imageUrl: string
  timestamp: number
}

export interface SystemConfig {
  mqttConfig: {
    host: string
    port: number
    username: string
    password: string
  }
  videoConfig: {
    primaryStream: string
    secondaryStream: string
  }
  weightConfig: {
    serialPort: string
    baudRate: number
    threshold: number
  }
}